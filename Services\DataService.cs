using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using FinancialTracker.Data;
using FinancialTracker.Models;

namespace FinancialTracker.Services
{
    public interface IDataService
    {
        Task<List<Project>> GetProjectsAsync();
        Task<Project?> GetProjectByIdAsync(int id);
        Task<Project> AddProjectAsync(Project project);
        Task<Project> UpdateProjectAsync(Project project);
        Task<bool> DeleteProjectAsync(int id);

        Task<List<Invoice>> GetInvoicesAsync();
        Task<List<Invoice>> GetInvoicesByProjectAsync(int projectId);
        Task<Invoice?> GetInvoiceByIdAsync(int id);
        Task<Invoice> AddInvoiceAsync(Invoice invoice);
        Task<Invoice> UpdateInvoiceAsync(Invoice invoice);
        Task<bool> DeleteInvoiceAsync(int id);
        Task<bool> RemoveInvoiceFromCommitmentAsync(int invoiceId);

        Task<List<Commitment>> GetCommitmentsAsync();
        Task<List<Commitment>> GetCommitmentsByProjectAsync(int projectId);
        Task<Commitment?> GetCommitmentByIdAsync(int id);
        Task<Commitment> AddCommitmentAsync(Commitment commitment);
        Task<Commitment> UpdateCommitmentAsync(Commitment commitment);
        Task<bool> DeleteCommitmentAsync(int id);



        Task<DashboardData> GetDashboardDataAsync();
        Task<List<ProjectSummary>> GetProjectsSummaryAsync();
    }

    public class DashboardData
    {
        public int TotalProjects { get; set; }
        public int TotalInvoices { get; set; }
        public int TotalCommitments { get; set; }

        // إحصائيات مفصلة للارتباطات
        public int TaskCommitments { get; set; }
        public int ServiceCommitments { get; set; }
        public int OtherCommitments { get; set; }

        public decimal TotalInvoiceAmount { get; set; }
        public decimal TotalPaidAmount { get; set; }
        public decimal TotalUnpaidAmount { get; set; }
        public int PaidInvoicesCount { get; set; }
        public int UnpaidInvoicesCount { get; set; }

        // إحصائيات مالية للارتباطات
        public decimal TaskCommitmentsAmount { get; set; }
        public decimal ServiceCommitmentsAmount { get; set; }
        public decimal OtherCommitmentsAmount { get; set; }
    }

    public class DataService : IDataService
    {
        private readonly FinancialContext _context;

        public DataService(FinancialContext context)
        {
            _context = context;
        }

        public async Task<List<Project>> GetProjectsAsync()
        {
            return await _context.Projects
                .Include(p => p.Invoices)
                .Include(p => p.Commitments)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<Project?> GetProjectByIdAsync(int id)
        {
            return await _context.Projects
                .Include(p => p.Invoices)
                .Include(p => p.Commitments)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Project> AddProjectAsync(Project project)
        {
            _context.Projects.Add(project);
            await _context.SaveChangesAsync();
            return project;
        }

        public async Task<Project> UpdateProjectAsync(Project project)
        {
            _context.Projects.Update(project);
            await _context.SaveChangesAsync();
            return project;
        }

        public async Task<bool> DeleteProjectAsync(int id)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var project = await _context.Projects
                    .Include(p => p.Invoices)
                        .ThenInclude(i => i.Replies)
                    .Include(p => p.Commitments)
                        .ThenInclude(c => c.Replies)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (project == null) return false;

                // Delete all replies for invoices
                foreach (var invoice in project.Invoices)
                {
                    if (invoice.Replies.Any())
                    {
                        _context.Replies.RemoveRange(invoice.Replies);
                    }
                }

                // Delete all replies for commitments
                foreach (var commitment in project.Commitments)
                {
                    if (commitment.Replies.Any())
                    {
                        _context.Replies.RemoveRange(commitment.Replies);
                    }
                }

                // Delete all invoices
                if (project.Invoices.Any())
                {
                    _context.Invoices.RemoveRange(project.Invoices);
                }

                // Delete all commitments
                if (project.Commitments.Any())
                {
                    _context.Commitments.RemoveRange(project.Commitments);
                }



                // Finally delete the project
                _context.Projects.Remove(project);

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return true;
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<List<Invoice>> GetInvoicesAsync()
        {
            return await _context.Invoices
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .OrderByDescending(i => i.CreatedDate)
                .ToListAsync();
        }

        public async Task<List<Invoice>> GetInvoicesByProjectAsync(int projectId)
        {
            return await _context.Invoices
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Where(i => i.ProjectId == projectId)
                .OrderByDescending(i => i.CreatedDate)
                .ToListAsync();
        }

        public async Task<Invoice?> GetInvoiceByIdAsync(int id)
        {
            return await _context.Invoices
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .FirstOrDefaultAsync(i => i.Id == id);
        }

        public async Task<Invoice> AddInvoiceAsync(Invoice invoice)
        {
            // إذا كانت الفاتورة مرتبطة بارتباط، اجعل نوعها نفس نوع الارتباط
            if (invoice.CommitmentId.HasValue)
            {
                var commitment = await _context.Commitments
                    .Include(c => c.Invoices)
                    .FirstOrDefaultAsync(c => c.Id == invoice.CommitmentId.Value);

                if (commitment != null)
                {
                    invoice.Type = commitment.Type;

                    // Calculate the invoice amount in the commitment's currency
                    var invoiceAmountInCommitmentCurrency = invoice.AmountUSD * (invoice.ExchangeRate / commitment.ExchangeRate);

                    if (commitment.RemainingAmount < invoiceAmountInCommitmentCurrency)
                    {
                        throw new Exception("The remaining amount in the commitment is not enough to cover this invoice.");
                    }
                }
            }

            _context.Invoices.Add(invoice);
            await _context.SaveChangesAsync();
            return invoice;
        }

        public async Task<Invoice> UpdateInvoiceAsync(Invoice invoice)
        {
            // إذا كانت الفاتورة مرتبطة بارتباط، تأكد من أن نوعها يطابق نوع الارتباط
            if (invoice.CommitmentId.HasValue)
            {
                var commitment = await _context.Commitments
                    .FirstOrDefaultAsync(c => c.Id == invoice.CommitmentId.Value);

                if (commitment != null)
                {
                    invoice.Type = commitment.Type;
                }
            }

            _context.Invoices.Update(invoice);
            await _context.SaveChangesAsync();
            return invoice;
        }

        public async Task<bool> DeleteInvoiceAsync(int id)
        {
            var invoice = await _context.Invoices.FindAsync(id);
            if (invoice == null) return false;

            _context.Invoices.Remove(invoice);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RemoveInvoiceFromCommitmentAsync(int invoiceId)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            if (invoice == null) return false;

            invoice.CommitmentId = null;
            _context.Invoices.Update(invoice);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<Commitment>> GetCommitmentsAsync()
        {
            return await _context.Commitments
                .Include(c => c.Project)
                .OrderByDescending(c => c.CreatedDate)
                .ToListAsync();
        }

        public async Task<List<Commitment>> GetCommitmentsByProjectAsync(int projectId)
        {
            return await _context.Commitments
                .Include(c => c.Project)
                .Where(c => c.ProjectId == projectId)
                .OrderByDescending(c => c.CreatedDate)
                .ToListAsync();
        }

        public async Task<Commitment?> GetCommitmentByIdAsync(int id)
        {
            return await _context.Commitments
                .Include(c => c.Project)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<Commitment> AddCommitmentAsync(Commitment commitment)
        {
            _context.Commitments.Add(commitment);
            await _context.SaveChangesAsync();
            return commitment;
        }

        public async Task<Commitment> UpdateCommitmentAsync(Commitment commitment)
        {
            // احفظ النوع القديم للمقارنة
            var existingCommitment = await _context.Commitments.AsNoTracking()
                .FirstOrDefaultAsync(c => c.Id == commitment.Id);

            _context.Commitments.Update(commitment);
            await _context.SaveChangesAsync();

            // إذا تغير النوع، حدث الفواتير المرتبطة
            if (existingCommitment != null && existingCommitment.Type != commitment.Type)
            {
                await UpdateRelatedInvoicesTypeAsync(commitment.Id, commitment.Type);
            }

            return commitment;
        }

        // دالة لتحديث أنواع الفواتير المرتبطة بارتباط معين
        private async Task UpdateRelatedInvoicesTypeAsync(int commitmentId, string newCommitmentType)
        {
            try
            {
                var relatedInvoices = await _context.Invoices
                    .Where(i => i.CommitmentId == commitmentId)
                    .ToListAsync();

                if (relatedInvoices.Any())
                {
                    foreach (var invoice in relatedInvoices)
                    {
                        invoice.Type = newCommitmentType;
                    }

                    await _context.SaveChangesAsync();
                }
            }
            catch (System.Exception ex)
            {
                // يمكن إضافة logging هنا إذا لزم الأمر
                System.Diagnostics.Debug.WriteLine($"Error updating related invoices: {ex.Message}");
            }
        }

        public async Task<bool> DeleteCommitmentAsync(int id)
        {
            var commitment = await _context.Commitments.FindAsync(id);
            if (commitment == null) return false;

            _context.Commitments.Remove(commitment);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<DashboardData> GetDashboardDataAsync()
        {
            var invoices = await _context.Invoices.ToListAsync();
            var projects = await _context.Projects.ToListAsync();
            var commitments = await _context.Commitments.ToListAsync();

            // Calculate total amounts using manual values where available
            var totalTaskAmount = 0m;
            var totalServiceAmount = 0m;
            var totalOtherAmount = 0m;

            foreach (var project in projects)
            {
                var projectCommitments = commitments.Where(c => c.ProjectId == project.Id).ToList();

                var taskCommitments = projectCommitments.Where(c =>
                    c.Type == "مهمات" ||
                    c.Type.Contains("مهمة") ||
                    c.Type.Contains("مهام") ||
                    c.Type.ToLower().Contains("task") ||
                    c.Type.ToLower().Contains("software") ||
                    c.Type.ToLower().Contains("hardware")).ToList();

                var serviceCommitments = projectCommitments.Where(c =>
                    c.Type == "خدمات" ||
                    c.Type.Contains("خدمة") ||
                    c.Type.ToLower().Contains("service")).ToList();

                var otherCommitments = projectCommitments.Where(c =>
                    !taskCommitments.Contains(c) &&
                    !serviceCommitments.Contains(c)).ToList();

                // Use manual amounts if available, otherwise calculate from commitments
                totalTaskAmount += project.ManualTasksAmount > 0 ? project.ManualTasksAmount : taskCommitments.Sum(c => c.AmountUSD);
                totalServiceAmount += project.ManualServicesAmount > 0 ? project.ManualServicesAmount : serviceCommitments.Sum(c => c.AmountUSD);
                totalOtherAmount += otherCommitments.Sum(c => c.AmountUSD);
            }

            // Count commitments by type across all projects
            var allTaskCommitments = commitments.Where(c =>
                c.Type == "مهمات" ||
                c.Type.Contains("مهمة") ||
                c.Type.Contains("مهام") ||
                c.Type.ToLower().Contains("task") ||
                c.Type.ToLower().Contains("software") ||
                c.Type.ToLower().Contains("hardware")).ToList();

            var allServiceCommitments = commitments.Where(c =>
                c.Type == "خدمات" ||
                c.Type.Contains("خدمة") ||
                c.Type.ToLower().Contains("service")).ToList();

            var allOtherCommitments = commitments.Where(c =>
                !allTaskCommitments.Contains(c) &&
                !allServiceCommitments.Contains(c)).ToList();

            return new DashboardData
            {
                TotalProjects = projects.Count,
                TotalInvoices = invoices.Count,
                TotalCommitments = commitments.Count,

                // Detailed commitment statistics
                TaskCommitments = allTaskCommitments.Count,
                ServiceCommitments = allServiceCommitments.Count,
                OtherCommitments = allOtherCommitments.Count,

                TotalInvoiceAmount = invoices.Sum(i => i.AmountUSD),
                TotalPaidAmount = invoices.Sum(i => i.PaidAmount),
                TotalUnpaidAmount = invoices.Sum(i => i.RemainingAmount),
                PaidInvoicesCount = invoices.Count(i => i.IsFullyPaid),
                UnpaidInvoicesCount = invoices.Count(i => !i.IsFullyPaid),

                // Financial statistics using manual amounts where available
                TaskCommitmentsAmount = totalTaskAmount,
                ServiceCommitmentsAmount = totalServiceAmount,
                OtherCommitmentsAmount = totalOtherAmount
            };
        }

        public async Task<List<ProjectSummary>> GetProjectsSummaryAsync()
        {
            var projects = await _context.Projects.ToListAsync();
            var invoices = await _context.Invoices.ToListAsync();
            var commitments = await _context.Commitments.ToListAsync();

            var projectsSummary = projects.Select(project =>
            {
                var projectInvoices = invoices.Where(i => i.ProjectId == project.Id).ToList();
                var projectCommitments = commitments.Where(c => c.ProjectId == project.Id).ToList();

                // Calculate task and service amounts - use manual values if available, otherwise calculate from commitments
                var taskCommitments = projectCommitments.Where(c =>
                    c.Type == "مهمات" ||
                    c.Type.Contains("مهمة") ||
                    c.Type.Contains("مهام") ||
                    c.Type.ToLower().Contains("task") ||
                    c.Type.ToLower().Contains("software") ||
                    c.Type.ToLower().Contains("hardware")).ToList();

                var serviceCommitments = projectCommitments.Where(c =>
                    c.Type == "خدمات" ||
                    c.Type.Contains("خدمة") ||
                    c.Type.ToLower().Contains("service")).ToList();

                var otherCommitments = projectCommitments.Where(c =>
                    !taskCommitments.Contains(c) &&
                    !serviceCommitments.Contains(c)).ToList();

                // Use manual amounts if available, otherwise calculate from commitments
                var taskAmount = project.ManualTasksAmount > 0 ? project.ManualTasksAmount : taskCommitments.Sum(c => c.AmountUSD);
                var serviceAmount = project.ManualServicesAmount > 0 ? project.ManualServicesAmount : serviceCommitments.Sum(c => c.AmountUSD);

                var totalCost = projectCommitments.Sum(c => c.AmountUSD);
                var totalPaid = projectInvoices.Sum(i => i.PaidAmount);

                // حساب معلومات الملفات
                var filesCount = projectInvoices.Count(i => !string.IsNullOrEmpty(i.AttachedFileName)) +
                               projectCommitments.Count(c => !string.IsNullOrEmpty(c.AttachedFileName));

                return new ProjectSummary
                {
                    Id = project.Id,
                    Name = project.Name,
                    Description = project.Description,
                    Status = project.Status,
                    CreatedDate = project.CreatedDate,
                    TotalCost = totalCost,
                    TotalPaid = totalPaid,
                    TotalInvoices = projectInvoices.Count,
                    TotalCommitments = projectCommitments.Count,
                    PaidInvoices = projectInvoices.Count(i => i.IsFullyPaid),
                    UnpaidInvoices = projectInvoices.Count(i => !i.IsFullyPaid),

                    // Detailed commitment statistics - use manual amounts if available
                    TaskCommitments = taskCommitments.Count,
                    ServiceCommitments = serviceCommitments.Count,
                    OtherCommitments = otherCommitments.Count,
                    TaskCommitmentsAmount = taskAmount,
                    ServiceCommitmentsAmount = serviceAmount,
                    OtherCommitmentsAmount = otherCommitments.Sum(c => c.AmountUSD),

                    // معلومات الملفات
                    FilesCount = filesCount,
                    TotalFileSize = 0 // سيتم حسابها لاحقاً إذا لزم الأمر
                };
            }).ToList();

            return projectsSummary;
        }


    }
}
