using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FinancialTracker.Helpers;

namespace FinancialTracker.Models
{
    public class Project
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string Description { get; set; } = string.Empty;

        [MaxLength(50)]
        public string Status { get; set; } = "Active";

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
        public virtual ICollection<Commitment> Commitments { get; set; } = new List<Commitment>();
        public virtual ICollection<Letter> Letters { get; set; } = new List<Letter>();
        public virtual ICollection<ProjectSite> ProjectSites { get; set; } = new List<ProjectSite>();

        // Computed properties for multi-site support
        [NotMapped]
        public decimal TotalAmountAllSites => GetTotalAmountAllSites();

        [NotMapped]
        public decimal PaidAmountAllSites => GetPaidAmountAllSites();

        [NotMapped]
        public decimal RemainingAmountAllSites => TotalAmountAllSites - PaidAmountAllSites;

        private decimal GetTotalAmountAllSites()
        {
            decimal total = 0;
            foreach (var invoice in Invoices)
            {
                total += invoice.AmountUSD;
            }
            return MathHelper.RoundCustom(total);
        }

        private decimal GetPaidAmountAllSites()
        {
            decimal total = 0;
            foreach (var invoice in Invoices)
            {
                total += invoice.PaidAmount;
            }
            return MathHelper.RoundCustom(total);
        }
    }
}
