// <auto-generated />
using System;
using FinancialTracker.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace FinancialTracker.Migrations
{
    [DbContext(typeof(FinancialContext))]
    partial class FinancialContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "6.0.25");

            modelBuilder.Entity("FinancialTracker.Models.Commitment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("AmountUSD")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("AttachedFileName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("AttachedFilePath")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("decimal(10,4)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("SiteId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.HasIndex("SiteId");

                    b.ToTable("Commitments");
                });

            modelBuilder.Entity("FinancialTracker.Models.Invoice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("AmountUSD")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("AttachedFileName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("AttachedFilePath")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CommitmentId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("decimal(10,4)");

                    b.Property<DateTime>("InvoiceDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsPaid")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LetterFileName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("LetterFilePath")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PaidAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<DateTime?>("PaidDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("SignatureDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("SiteId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Type")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("Task");

                    b.HasKey("Id");

                    b.HasIndex("CommitmentId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("SiteId");

                    b.ToTable("Invoices");
                });

            modelBuilder.Entity("FinancialTracker.Models.Project", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("POAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("PODate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("Active");

                    b.HasKey("Id");

                    b.ToTable("Projects");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "University Management System",
                            Name = "UMS",
                            POAmount = 0m,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2024, 1, 6, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Banking and Finance System",
                            Name = "BNG",
                            POAmount = 0m,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 3,
                            CreatedDate = new DateTime(2024, 1, 11, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Authentication and Authorization",
                            Name = "AAA",
                            POAmount = 0m,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 4,
                            CreatedDate = new DateTime(2024, 1, 16, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Network Time Protocol",
                            Name = "NTP",
                            POAmount = 0m,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 5,
                            CreatedDate = new DateTime(2024, 1, 21, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "High Performance Business Exchange",
                            Name = "HPBX",
                            POAmount = 0m,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 6,
                            CreatedDate = new DateTime(2024, 1, 26, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Enterprise Resource Planning System",
                            Name = "ERP System",
                            POAmount = 0m,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 7,
                            CreatedDate = new DateTime(2024, 1, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Customer Relationship Management Platform",
                            Name = "CRM Platform",
                            POAmount = 0m,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 8,
                            CreatedDate = new DateTime(2024, 2, 5, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "E-Commerce Website and Mobile App",
                            Name = "E-Commerce",
                            POAmount = 0m,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 9,
                            CreatedDate = new DateTime(2024, 2, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Business Intelligence and Data Analytics Platform",
                            Name = "Data Analytics",
                            POAmount = 0m,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 10,
                            CreatedDate = new DateTime(2024, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Cross-Platform Mobile Application",
                            Name = "Mobile App",
                            POAmount = 0m,
                            Status = "Active"
                        });
                });

            modelBuilder.Entity("FinancialTracker.Models.ProjectSite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("ActualCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("AssignedDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("EstimatedBudget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("SiteId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("Active");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.HasIndex("ProjectId", "SiteId")
                        .IsUnique();

                    b.ToTable("ProjectSites");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            AssignedDate = new DateTime(2024, 1, 6, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 1,
                            SiteId = 1,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 2,
                            AssignedDate = new DateTime(2024, 1, 6, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 1,
                            SiteId = 2,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 3,
                            AssignedDate = new DateTime(2024, 1, 11, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 2,
                            SiteId = 1,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 4,
                            AssignedDate = new DateTime(2024, 1, 11, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 2,
                            SiteId = 2,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 5,
                            AssignedDate = new DateTime(2024, 1, 16, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 3,
                            SiteId = 1,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 6,
                            AssignedDate = new DateTime(2024, 1, 16, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 3,
                            SiteId = 2,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 7,
                            AssignedDate = new DateTime(2024, 1, 21, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 4,
                            SiteId = 1,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 8,
                            AssignedDate = new DateTime(2024, 1, 21, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 4,
                            SiteId = 2,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 9,
                            AssignedDate = new DateTime(2024, 1, 26, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 5,
                            SiteId = 1,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 10,
                            AssignedDate = new DateTime(2024, 1, 26, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 5,
                            SiteId = 2,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 11,
                            AssignedDate = new DateTime(2024, 1, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 6,
                            SiteId = 1,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 12,
                            AssignedDate = new DateTime(2024, 1, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 6,
                            SiteId = 2,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 13,
                            AssignedDate = new DateTime(2024, 2, 5, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 7,
                            SiteId = 1,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 14,
                            AssignedDate = new DateTime(2024, 2, 5, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 7,
                            SiteId = 2,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 15,
                            AssignedDate = new DateTime(2024, 2, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 8,
                            SiteId = 1,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 16,
                            AssignedDate = new DateTime(2024, 2, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 8,
                            SiteId = 2,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 17,
                            AssignedDate = new DateTime(2024, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 9,
                            SiteId = 1,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 18,
                            AssignedDate = new DateTime(2024, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 9,
                            SiteId = 2,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 19,
                            AssignedDate = new DateTime(2024, 2, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 10,
                            SiteId = 1,
                            Status = "Active"
                        },
                        new
                        {
                            Id = 20,
                            AssignedDate = new DateTime(2024, 2, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ProjectId = 10,
                            SiteId = 2,
                            Status = "Active"
                        });
                });

            modelBuilder.Entity("FinancialTracker.Models.Reply", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AttachedFileName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("AttachedFilePath")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("CommitmentId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("InvoiceId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CommitmentId");

                    b.HasIndex("InvoiceId");

                    b.ToTable("Replies");
                });

            modelBuilder.Entity("FinancialTracker.Models.Site", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("Active");

                    b.HasKey("Id");

                    b.ToTable("Sites");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "الموقع الرئيسي الأول",
                            DisplayName = "الموقع الأول - NN1",
                            Name = "nn1",
                            Status = "Active"
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "الموقع الرئيسي الثاني",
                            DisplayName = "الموقع الثاني - NN2",
                            Name = "nn2",
                            Status = "Active"
                        });
                });

            modelBuilder.Entity("FinancialTracker.Models.Commitment", b =>
                {
                    b.HasOne("FinancialTracker.Models.Project", "Project")
                        .WithMany("Commitments")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FinancialTracker.Models.Site", "Site")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Project");

                    b.Navigation("Site");
                });

            modelBuilder.Entity("FinancialTracker.Models.Invoice", b =>
                {
                    b.HasOne("FinancialTracker.Models.Commitment", "Commitment")
                        .WithMany("Invoices")
                        .HasForeignKey("CommitmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("FinancialTracker.Models.Project", "Project")
                        .WithMany("Invoices")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FinancialTracker.Models.Site", "Site")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Commitment");

                    b.Navigation("Project");

                    b.Navigation("Site");
                });

            modelBuilder.Entity("FinancialTracker.Models.ProjectSite", b =>
                {
                    b.HasOne("FinancialTracker.Models.Project", "Project")
                        .WithMany("ProjectSites")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FinancialTracker.Models.Site", "Site")
                        .WithMany("ProjectSites")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Project");

                    b.Navigation("Site");
                });

            modelBuilder.Entity("FinancialTracker.Models.Reply", b =>
                {
                    b.HasOne("FinancialTracker.Models.Commitment", "Commitment")
                        .WithMany("Replies")
                        .HasForeignKey("CommitmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FinancialTracker.Models.Invoice", "Invoice")
                        .WithMany("Replies")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Commitment");

                    b.Navigation("Invoice");
                });

            modelBuilder.Entity("FinancialTracker.Models.Commitment", b =>
                {
                    b.Navigation("Invoices");

                    b.Navigation("Replies");
                });

            modelBuilder.Entity("FinancialTracker.Models.Invoice", b =>
                {
                    b.Navigation("Replies");
                });

            modelBuilder.Entity("FinancialTracker.Models.Project", b =>
                {
                    b.Navigation("Commitments");

                    b.Navigation("Invoices");

                    b.Navigation("ProjectSites");
                });

            modelBuilder.Entity("FinancialTracker.Models.Site", b =>
                {
                    b.Navigation("ProjectSites");
                });
#pragma warning restore 612, 618
        }
    }
}
