<Window x:Class="FinancialTracker.InvoiceDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Invoice Details" 
        Height="600" 
        Width="600"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Invoice Information" FontSize="20" FontWeight="Bold" Margin="0,0,0,16"/>

        <!-- Form -->
        <ScrollViewer Grid.Row="1">
            <StackPanel>
                <TextBox x:Name="InvoiceNumberTextBox"
                         materialDesign:HintAssist.Hint="Invoice Number"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="100"
                         Margin="0,0,0,16"/>

                <ComboBox x:Name="ProjectComboBox"
                          materialDesign:HintAssist.Hint="Select Project"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          DisplayMemberPath="Name"
                          SelectedValuePath="Id"
                          Margin="0,0,0,16"
                          SelectionChanged="ProjectComboBox_SelectionChanged"/>

                <ComboBox x:Name="SiteComboBox"
                          materialDesign:HintAssist.Hint="Select Site"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          DisplayMemberPath="DisplayName"
                          SelectedValuePath="Id"
                          Margin="0,0,0,16"/>

                <ComboBox x:Name="CommitmentComboBox"
                          materialDesign:HintAssist.Hint="Select Commitment (Optional)"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          DisplayMemberPath="Title"
                          SelectedValuePath="Id"
                          Margin="0,0,0,16"/>



                <ComboBox x:Name="TypeComboBox"
                          materialDesign:HintAssist.Hint="Invoice Type"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="0,0,0,16"
                          SelectionChanged="TypeComboBox_SelectionChanged">
                    <ComboBoxItem Content="Software Tasks"/>
                    <ComboBoxItem Content="Hardware Tasks"/>
                    <ComboBoxItem Content="Services"/>
                    <ComboBoxItem Content="Other"/>
                </ComboBox>

                <TextBox x:Name="CustomTypeTextBox"
                         materialDesign:HintAssist.Hint="Custom Type"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,0,16"
                         Visibility="Collapsed"/>

                <TextBox x:Name="AmountTextBox"
                         materialDesign:HintAssist.Hint="Amount (USD)"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,0,16"/>
                
                <TextBox x:Name="ExchangeRateTextBox"
                         materialDesign:HintAssist.Hint="Exchange Rate"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,0,16"/>

                <TextBox x:Name="PaidAmountTextBox"
                         materialDesign:HintAssist.Hint="Paid Amount (USD)"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,0,16"/>

                <DatePicker x:Name="InvoiceDatePicker"
                            materialDesign:HintAssist.Hint="Invoice Date"
                            Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                            Margin="0,0,0,16"/>
                            
                <DatePicker x:Name="SignatureDatePicker"
                            materialDesign:HintAssist.Hint="Signature Date"
                            Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                            Margin="0,0,0,16"/>

                <TextBox x:Name="DescriptionTextBox"
                         materialDesign:HintAssist.Hint="Description"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="500"
                         Height="100"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         Margin="0,0,0,16"/>



                <!-- File Attachment Section -->
                <materialDesign:Card Padding="16" Margin="0,8">
                    <StackPanel>
                        <TextBlock Text="مرفقات الفاتورة" FontWeight="Medium" Margin="0,0,0,8"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="AttachedFileTextBox"
                                     Grid.Column="0"
                                     materialDesign:HintAssist.Hint="لم يتم اختيار ملف"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     IsReadOnly="True"/>

                            <Button x:Name="SelectFileButton"
                                    Grid.Column="1"
                                    Content="رفع ملف"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Margin="8,0,0,0"
                                    Click="SelectFileButton_Click"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Letter Attachment Section -->
                <materialDesign:Card Padding="16" Margin="0,8">
                    <StackPanel>
                        <TextBlock Text="رفع الخطاب" FontWeight="Medium" Margin="0,0,0,8"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="LetterFileTextBox"
                                     Grid.Column="0"
                                     materialDesign:HintAssist.Hint="لم يتم رفع خطاب"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     IsReadOnly="True"/>

                            <Button x:Name="SelectLetterButton"
                                    Grid.Column="1"
                                    Content="رفع الخطاب"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Margin="8,0,0,0"
                                    Click="SelectLetterButton_Click"/>

                            <Button x:Name="ViewLetterButton"
                                    Grid.Column="2"
                                    Content="الخطاب"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Margin="8,0,0,0"
                                    Click="ViewLetterButton_Click"
                                    Visibility="Collapsed"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button Content="Cancel"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    Click="CancelButton_Click"/>
            <Button Content="Save"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Padding="16,8"
                    Click="SaveButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
