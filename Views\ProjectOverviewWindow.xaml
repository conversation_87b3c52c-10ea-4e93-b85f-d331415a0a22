<Window x:Class="FinancialTracker.ProjectOverviewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
        Title="Project Overview" 
        Height="800" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <Style x:Key="StatCard" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="MinHeight" Value="100"/>
        </Style>
    </Window.Resources>

    <Grid>
        <TabControl Style="{StaticResource MaterialDesignTabControl}" Margin="16">
            
            <!-- Project Overview Tab -->
            <TabItem Header="Project Overview">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Project Header -->
                        <materialDesign:Card Grid.Row="0" Margin="0,0,0,16" Padding="20" Background="#1976D2">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock x:Name="ProjectNameText" Text="Project Name" FontSize="24" FontWeight="Bold" Foreground="White"/>
                                    <TextBlock x:Name="ProjectDescriptionText" Text="Project Description" FontSize="14" Foreground="White" Opacity="0.9" Margin="0,4,0,0"/>
                                    <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                                        <TextBlock Text="Status: " FontSize="12" Foreground="White" Opacity="0.8"/>
                                        <TextBlock x:Name="ProjectStatusText" Text="Active" FontSize="12" Foreground="White" FontWeight="Medium"/>
                                        <TextBlock Text=" | Created: " FontSize="12" Foreground="White" Opacity="0.8" Margin="16,0,0,0"/>
                                        <TextBlock x:Name="ProjectCreatedText" Text="2024-01-01" FontSize="12" Foreground="White" FontWeight="Medium"/>
                                    </StackPanel>
                                </StackPanel>
                                
                                <Button Grid.Column="1" Content="Edit Project" Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Foreground="White" BorderBrush="White" Click="EditProjectButton_Click"/>
                            </Grid>
                        </materialDesign:Card>

                        <!-- Financial Input Section -->
                        <materialDesign:Card Grid.Row="1" Margin="0,0,0,16" Padding="20">
                            <StackPanel>
                                <TextBlock Text="Financial Planning" FontSize="18" FontWeight="Bold" Margin="0,0,0,16"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                        <DatePicker x:Name="PODatePicker"
                                                   materialDesign:HintAssist.Hint="PO Date (Project Start)"
                                                   Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                                   Margin="0,0,0,12"/>
                                        <TextBox x:Name="POAmountTextBox"
                                                materialDesign:HintAssist.Hint="Total PO Amount ($)"
                                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                Margin="0,0,0,12"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="1" Margin="8,0">
                                        <TextBox x:Name="TasksAmountTextBox"
                                                materialDesign:HintAssist.Hint="Total Tasks Amount ($)"
                                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                Margin="0,0,0,12"/>
                                        <TextBox x:Name="ServicesAmountTextBox"
                                                materialDesign:HintAssist.Hint="Total Services Amount ($)"
                                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                Margin="0,0,0,12"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="2" Margin="8,0,0,0" VerticalAlignment="Center">
                                        <Button Content="Save Financial Data" Style="{StaticResource MaterialDesignRaisedButton}"
                                                Click="SaveFinancialDataButton_Click" Margin="0,0,0,8"/>
                                        <Button Content="Refresh Data" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                Click="RefreshDataButton_Click"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Financial Statistics Cards -->
                        <Grid Grid.Row="2" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <materialDesign:Card Grid.Column="0" Style="{StaticResource StatCard}" Background="#E3F2FD">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Assignment" Width="32" Height="32" Foreground="#1976D2" HorizontalAlignment="Center"/>
                                    <TextBlock Text="Tasks Amount" FontSize="12" Opacity="0.7" HorizontalAlignment="Center" Margin="0,8,0,4"/>
                                    <TextBlock x:Name="TasksAmountDisplay" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#1976D2" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Grid.Column="1" Style="{StaticResource StatCard}" Background="#FFF3E0">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Wrench" Width="32" Height="32" Foreground="#F57C00" HorizontalAlignment="Center"/>
                                    <TextBlock Text="Services Amount" FontSize="12" Opacity="0.7" HorizontalAlignment="Center" Margin="0,8,0,4"/>
                                    <TextBlock x:Name="ServicesAmountDisplay" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#F57C00" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Grid.Column="2" Style="{StaticResource StatCard}" Background="#E8F5E8">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="CashMultiple" Width="32" Height="32" Foreground="#388E3C" HorizontalAlignment="Center"/>
                                    <TextBlock Text="Total Spent" FontSize="12" Opacity="0.7" HorizontalAlignment="Center" Margin="0,8,0,4"/>
                                    <TextBlock x:Name="TotalSpentDisplay" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#388E3C" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Grid.Column="3" Style="{StaticResource StatCard}" Background="#FCE4EC">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="TrendingUp" Width="32" Height="32" Foreground="#C2185B" HorizontalAlignment="Center"/>
                                    <TextBlock Text="Remaining" FontSize="12" Opacity="0.7" HorizontalAlignment="Center" Margin="0,8,0,4"/>
                                    <TextBlock x:Name="RemainingAmountDisplay" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#C2185B" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </Grid>

                        <!-- Progress and Summary -->
                        <Grid Grid.Row="3" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0" Padding="20">
                                <StackPanel>
                                    <TextBlock Text="Project Progress" FontSize="16" FontWeight="Bold" Margin="0,0,0,16"/>
                                    
                                    <Grid Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Completion:" VerticalAlignment="Center"/>
                                        <ProgressBar Grid.Column="1" x:Name="CompletionProgressBar" Height="8" Margin="12,0" 
                                                   Style="{StaticResource MaterialDesignLinearProgressBar}"/>
                                        <TextBlock Grid.Column="2" x:Name="CompletionPercentageDisplay" Text="0%" VerticalAlignment="Center" FontWeight="Bold"/>
                                    </Grid>
                                    
                                    <Separator Margin="0,8"/>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="Invoices" FontSize="14" FontWeight="Medium" Margin="0,0,0,8"/>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="Count: " Opacity="0.7"/>
                                                <TextBlock x:Name="InvoicesCountDisplay" Text="0" FontWeight="Bold"/>
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="Total: " Opacity="0.7"/>
                                                <TextBlock x:Name="InvoicesTotalDisplay" Text="$0" FontWeight="Bold"/>
                                            </StackPanel>
                                        </StackPanel>
                                        
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="Commitments" FontSize="14" FontWeight="Medium" Margin="0,0,0,8"/>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="Count: " Opacity="0.7"/>
                                                <TextBlock x:Name="CommitmentsCountDisplay" Text="0" FontWeight="Bold"/>
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="Total: " Opacity="0.7"/>
                                                <TextBlock x:Name="CommitmentsTotalDisplay" Text="$0" FontWeight="Bold"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Grid.Column="1" Margin="8,0,0,0" Padding="20">
                                <StackPanel>
                                    <TextBlock Text="Quick Actions" FontSize="16" FontWeight="Bold" Margin="0,0,0,16"/>
                                    <Button Content="Add Invoice" Style="{StaticResource MaterialDesignRaisedButton}" 
                                            Margin="0,0,0,8" Click="AddInvoiceButton_Click"/>
                                    <Button Content="Add Commitment" Style="{StaticResource MaterialDesignOutlinedButton}" 
                                            Margin="0,0,0,8" Click="AddCommitmentButton_Click"/>
                                    <Button Content="View Reports" Style="{StaticResource MaterialDesignOutlinedButton}" 
                                            Click="ViewReportsButton_Click"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </Grid>

                        <!-- Charts Section -->
                        <materialDesign:Card Grid.Row="4" Padding="20">
                            <StackPanel>
                                <TextBlock Text="Financial Overview Charts" FontSize="16" FontWeight="Bold" Margin="0,0,0,16"/>
                                
                                <Grid Height="300">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <!-- Pie Chart for Amount Distribution -->
                                    <Border Grid.Column="0" BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,0,8,0">
                                        <StackPanel>
                                            <TextBlock Text="Amount Distribution" FontWeight="Medium" HorizontalAlignment="Center" Margin="0,8"/>
                                            <lvc:PieChart x:Name="AmountDistributionChart" Height="250" Margin="8"/>
                                        </StackPanel>
                                    </Border>
                                    
                                    <!-- Column Chart for Progress -->
                                    <Border Grid.Column="1" BorderBrush="#E0E0E0" BorderThickness="1" Margin="8,0,0,0">
                                        <StackPanel>
                                            <TextBlock Text="Financial Progress" FontWeight="Medium" HorizontalAlignment="Center" Margin="0,8"/>
                                            <lvc:CartesianChart x:Name="ProgressChart" Height="250" Margin="8"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- Invoices Tab -->
            <TabItem Header="Invoices">
                <Frame x:Name="InvoicesFrame" NavigationUIVisibility="Hidden"/>
            </TabItem>

            <!-- Commitments Tab -->
            <TabItem Header="Commitments">
                <Frame x:Name="CommitmentsFrame" NavigationUIVisibility="Hidden"/>
            </TabItem>

        </TabControl>
    </Grid>
</Window>
