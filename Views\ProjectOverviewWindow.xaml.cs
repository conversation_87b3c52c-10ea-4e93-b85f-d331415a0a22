using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;
using LiveCharts;
using LiveCharts.Wpf;

namespace FinancialTracker
{
    public partial class ProjectOverviewWindow : Window
    {
        private readonly int _projectId;
        private Project _project;

        public ProjectOverviewWindow(int projectId)
        {
            InitializeComponent();
            _projectId = projectId;
            LoadProjectData();
            SetupCharts();
        }

        private async void LoadProjectData()
        {
            try
            {
                _project = await App.DataService.GetProjectByIdAsync(_projectId);
                if (_project == null)
                {
                    MessageBox.Show("Project not found.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    Close();
                    return;
                }

                UpdateProjectInfo();
                UpdateFinancialInputs();
                UpdateStatistics();
                UpdateCharts();
                LoadTransactions();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading project: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateProjectInfo()
        {
            ProjectNameText.Text = _project.Name;
            ProjectDescriptionText.Text = string.IsNullOrEmpty(_project.Description) ? "No description" : _project.Description;
            ProjectStatusText.Text = _project.Status;
            ProjectCreatedText.Text = _project.CreatedDate.ToString("yyyy-MM-dd");
            Title = $"Project Overview - {_project.Name}";
        }

        private void UpdateFinancialInputs()
        {
            PODatePicker.SelectedDate = _project.PODate;
            POAmountTextBox.Text = _project.POAmount > 0 ? _project.POAmount.ToString("F2") : "";
            TasksAmountTextBox.Text = _project.ManualTasksAmount > 0 ? _project.ManualTasksAmount.ToString("F2") : "";
            ServicesAmountTextBox.Text = _project.ManualServicesAmount > 0 ? _project.ManualServicesAmount.ToString("F2") : "";
        }

        private async void UpdateStatistics()
        {
            try
            {
                // Load fresh data
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _projectId).ToList();

                var allCommitments = await App.DataService.GetCommitmentsAsync();
                var projectCommitments = allCommitments.Where(c => c.ProjectId == _projectId).ToList();

                // Calculate spent amounts by type
                var tasksSpent = projectInvoices.Where(i => i.Type == "Software Tasks").Sum(i => i.AmountUSD) +
                                projectCommitments.Where(c => c.Type == "Software Tasks").Sum(c => c.AmountUSD);
                var servicesSpent = projectInvoices.Where(i => i.Type == "Services").Sum(i => i.AmountUSD) +
                                   projectCommitments.Where(c => c.Type == "Services").Sum(c => c.AmountUSD);

                // Update displays
                TasksAmountDisplay.Text = $"${_project.TasksAmount:N0}";
                TasksSpentDisplay.Text = $"${tasksSpent:N0}";
                TasksRemainingDisplay.Text = $"${Math.Max(0, _project.TasksAmount - tasksSpent):N0}";

                ServicesAmountDisplay.Text = $"${_project.ServicesAmount:N0}";
                ServicesSpentDisplay.Text = $"${servicesSpent:N0}";
                ServicesRemainingDisplay.Text = $"${Math.Max(0, _project.ServicesAmount - servicesSpent):N0}";

                // Progress
                var completionPercentage = _project.POAmount > 0 ? (_project.SpentAmount / _project.POAmount) * 100 : 0;
                CompletionProgressBar.Value = Math.Min((double)completionPercentage, 100);
                CompletionPercentageDisplay.Text = $"{completionPercentage:F1}%";

                // Counts
                InvoicesCountDisplay.Text = projectInvoices.Count.ToString();
                InvoicesTotalDisplay.Text = $"${projectInvoices.Sum(i => i.AmountUSD):N0}";
                CommitmentsCountDisplay.Text = projectCommitments.Count.ToString();
                CommitmentsTotalDisplay.Text = $"${projectCommitments.Sum(c => c.AmountUSD):N0}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error updating statistics: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetupCharts()
        {
            // Setup pie chart for amount distribution
            AmountDistributionChart.Series = new SeriesCollection();
            
            // Setup progress chart
            ProgressChart.Series = new SeriesCollection();
            ProgressChart.AxisX.Add(new Axis
            {
                Title = "Category",
                Labels = new[] { "Tasks", "Services", "Spent", "Remaining" }
            });
            ProgressChart.AxisY.Add(new Axis
            {
                Title = "Amount ($)",
                LabelFormatter = value => value.ToString("C0")
            });
        }

        private void UpdateCharts()
        {
            try
            {
                // Update pie chart
                AmountDistributionChart.Series.Clear();
                AmountDistributionChart.Series.Add(new PieSeries
                {
                    Title = "Tasks",
                    Values = new ChartValues<decimal> { _project.TasksAmount },
                    DataLabels = true,
                    LabelPoint = chartPoint => $"${chartPoint.Y:N0}"
                });
                AmountDistributionChart.Series.Add(new PieSeries
                {
                    Title = "Services",
                    Values = new ChartValues<decimal> { _project.ServicesAmount },
                    DataLabels = true,
                    LabelPoint = chartPoint => $"${chartPoint.Y:N0}"
                });

                // Update progress chart
                ProgressChart.Series.Clear();
                ProgressChart.Series.Add(new ColumnSeries
                {
                    Title = "Financial Overview",
                    Values = new ChartValues<decimal> 
                    { 
                        _project.TasksAmount, 
                        _project.ServicesAmount, 
                        _project.SpentAmount, 
                        _project.RemainingFromPO 
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error updating charts: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveFinancialDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate and parse inputs
                if (!decimal.TryParse(POAmountTextBox.Text, out decimal poAmount) || poAmount < 0)
                {
                    MessageBox.Show("Please enter a valid PO amount.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(TasksAmountTextBox.Text, out decimal tasksAmount) || tasksAmount < 0)
                {
                    MessageBox.Show("Please enter a valid tasks amount.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(ServicesAmountTextBox.Text, out decimal servicesAmount) || servicesAmount < 0)
                {
                    MessageBox.Show("Please enter a valid services amount.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Update project
                _project.PODate = PODatePicker.SelectedDate;
                _project.POAmount = poAmount;
                _project.ManualTasksAmount = tasksAmount;
                _project.ManualServicesAmount = servicesAmount;

                await App.DataService.UpdateProjectAsync(_project);

                MessageBox.Show("Financial data saved successfully!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // Refresh displays
                UpdateStatistics();
                UpdateCharts();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving financial data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshDataButton_Click(object sender, RoutedEventArgs e)
        {
            LoadProjectData();
        }

        private void EditProjectButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProjectDialog(_project);
            if (dialog.ShowDialog() == true)
            {
                LoadProjectData();
            }
        }

        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new InvoiceDialog(null, _projectId);
            if (dialog.ShowDialog() == true)
            {
                UpdateStatistics();
                UpdateCharts();
            }
        }

        private void AddCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new CommitmentDialog(null, _projectId);
            if (dialog.ShowDialog() == true)
            {
                UpdateStatistics();
                UpdateCharts();
                LoadTransactions();
            }
        }

        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new InvoiceDialog(null, _projectId);
            if (dialog.ShowDialog() == true)
            {
                UpdateStatistics();
                UpdateCharts();
                LoadTransactions();
            }
        }

        private void RefreshTransactionsButton_Click(object sender, RoutedEventArgs e)
        {
            LoadTransactions();
            UpdateStatistics();
            UpdateCharts();
        }

        private async void LoadTransactions()
        {
            try
            {
                TransactionsPanel.Children.Clear();

                // Load invoices
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _projectId).OrderByDescending(i => i.CreatedDate).ToList();

                // Load commitments
                var allCommitments = await App.DataService.GetCommitmentsAsync();
                var projectCommitments = allCommitments.Where(c => c.ProjectId == _projectId).OrderByDescending(c => c.CreatedDate).ToList();

                // Add invoices section
                if (projectInvoices.Any())
                {
                    var invoicesHeader = new TextBlock
                    {
                        Text = "Invoices",
                        FontSize = 18,
                        FontWeight = FontWeights.Bold,
                        Margin = new Thickness(0, 0, 0, 12),
                        Foreground = new SolidColorBrush(Color.FromRgb(25, 118, 210))
                    };
                    TransactionsPanel.Children.Add(invoicesHeader);

                    foreach (var invoice in projectInvoices)
                    {
                        var card = CreateInvoiceCard(invoice);
                        TransactionsPanel.Children.Add(card);
                    }
                }

                // Add commitments section
                if (projectCommitments.Any())
                {
                    var commitmentsHeader = new TextBlock
                    {
                        Text = "Commitments",
                        FontSize = 18,
                        FontWeight = FontWeights.Bold,
                        Margin = new Thickness(0, 16, 0, 12),
                        Foreground = new SolidColorBrush(Color.FromRgb(245, 124, 0))
                    };
                    TransactionsPanel.Children.Add(commitmentsHeader);

                    foreach (var commitment in projectCommitments)
                    {
                        var card = CreateCommitmentCard(commitment);
                        TransactionsPanel.Children.Add(card);
                    }
                }

                if (!projectInvoices.Any() && !projectCommitments.Any())
                {
                    var emptyMessage = new TextBlock
                    {
                        Text = "No financial transactions found for this project.",
                        FontStyle = FontStyles.Italic,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(0, 50, 0, 0),
                        Opacity = 0.7
                    };
                    TransactionsPanel.Children.Add(emptyMessage);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading transactions: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewReportsButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement reports functionality
            MessageBox.Show("Reports functionality coming soon!", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private MaterialDesignThemes.Wpf.Card CreateInvoiceCard(Invoice invoice)
        {
            var card = new MaterialDesignThemes.Wpf.Card
            {
                Margin = new Thickness(0, 0, 0, 8),
                Padding = new Thickness(16),
                Background = new SolidColorBrush(Color.FromRgb(227, 242, 253))
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var infoPanel = new StackPanel();

            var titleText = new TextBlock
            {
                Text = $"Invoice #{invoice.Number}",
                FontWeight = FontWeights.Bold,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(25, 118, 210))
            };
            infoPanel.Children.Add(titleText);

            var detailsText = new TextBlock
            {
                Text = $"Type: {invoice.Type} | Amount: ${invoice.AmountUSD:N2} | Status: {invoice.SignatureStatus}",
                Opacity = 0.8,
                Margin = new Thickness(0, 4, 0, 0)
            };
            infoPanel.Children.Add(detailsText);

            var dateText = new TextBlock
            {
                Text = $"Created: {invoice.CreatedDate:yyyy-MM-dd}",
                Opacity = 0.6,
                FontSize = 12,
                Margin = new Thickness(0, 2, 0, 0)
            };
            infoPanel.Children.Add(dateText);

            Grid.SetColumn(infoPanel, 0);
            grid.Children.Add(infoPanel);

            var editButton = new Button
            {
                Content = "Edit",
                Style = (Style)FindResource("MaterialDesignOutlinedButton"),
                Margin = new Thickness(8, 0, 0, 0),
                Tag = invoice
            };
            editButton.Click += EditInvoiceButton_Click;
            Grid.SetColumn(editButton, 1);
            grid.Children.Add(editButton);

            card.Content = grid;
            return card;
        }

        private MaterialDesignThemes.Wpf.Card CreateCommitmentCard(Commitment commitment)
        {
            var card = new MaterialDesignThemes.Wpf.Card
            {
                Margin = new Thickness(0, 0, 0, 8),
                Padding = new Thickness(16),
                Background = new SolidColorBrush(Color.FromRgb(255, 243, 224))
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var infoPanel = new StackPanel();

            var titleText = new TextBlock
            {
                Text = $"Commitment #{commitment.Number}",
                FontWeight = FontWeights.Bold,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(245, 124, 0))
            };
            infoPanel.Children.Add(titleText);

            var detailsText = new TextBlock
            {
                Text = $"Type: {commitment.Type} | Amount: ${commitment.AmountUSD:N2}",
                Opacity = 0.8,
                Margin = new Thickness(0, 4, 0, 0)
            };
            infoPanel.Children.Add(detailsText);

            var dateText = new TextBlock
            {
                Text = $"Created: {commitment.CreatedDate:yyyy-MM-dd}",
                Opacity = 0.6,
                FontSize = 12,
                Margin = new Thickness(0, 2, 0, 0)
            };
            infoPanel.Children.Add(dateText);

            Grid.SetColumn(infoPanel, 0);
            grid.Children.Add(infoPanel);

            var editButton = new Button
            {
                Content = "Edit",
                Style = (Style)FindResource("MaterialDesignOutlinedButton"),
                Margin = new Thickness(8, 0, 0, 0),
                Tag = commitment
            };
            editButton.Click += EditCommitmentButton_Click;
            Grid.SetColumn(editButton, 1);
            grid.Children.Add(editButton);

            card.Content = grid;
            return card;
        }

        private void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var invoice = button?.Tag as Invoice;
            if (invoice != null)
            {
                var dialog = new InvoiceDialog(invoice, _projectId);
                if (dialog.ShowDialog() == true)
                {
                    UpdateStatistics();
                    UpdateCharts();
                    LoadTransactions();
                }
            }
        }

        private void EditCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var commitment = button?.Tag as Commitment;
            if (commitment != null)
            {
                var dialog = new CommitmentDialog(commitment, _projectId);
                if (dialog.ShowDialog() == true)
                {
                    UpdateStatistics();
                    UpdateCharts();
                    LoadTransactions();
                }
            }
        }
    }
}
