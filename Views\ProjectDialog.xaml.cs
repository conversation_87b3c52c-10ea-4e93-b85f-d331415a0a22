using System;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;

namespace FinancialTracker
{
    public partial class ProjectDialog : Window
    {
        private Project? _project;
        private bool _isEdit;

        public ProjectDialog(Project? project = null)
        {
            InitializeComponent();
            _project = project;
            _isEdit = project != null;

            if (_isEdit && _project != null)
            {
                Title = "تحرير المشروع";
                NameTextBox.Text = _project.Name;
                DescriptionTextBox.Text = _project.Description;
                PODatePicker.SelectedDate = _project.PODate;
                POAmountTextBox.Text = _project.POAmount > 0 ? _project.POAmount.ToString("F2") : "";

                // Set status - map English to Arabic
                var statusMapping = new System.Collections.Generic.Dictionary<string, int>
                {
                    { "Active", 0 }, { "نشط", 0 },
                    { "On Hold", 1 }, { "معلق", 1 },
                    { "Completed", 2 }, { "مكتمل", 2 },
                    { "Cancelled", 3 }, { "ملغي", 3 }
                };

                if (statusMapping.ContainsKey(_project.Status))
                {
                    StatusComboBox.SelectedIndex = statusMapping[_project.Status];
                }
                else
                {
                    StatusComboBox.SelectedIndex = 0; // Default to Active
                }
            }
            else
            {
                Title = "إضافة مشروع جديد";
                StatusComboBox.SelectedIndex = 0; // Default to Active
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(NameTextBox.Text))
                {
                    MessageBox.Show("اسم المشروع مطلوب.", "خطأ في التحقق",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Validate PO Amount
                decimal poAmount = 0;
                if (!string.IsNullOrWhiteSpace(POAmountTextBox.Text))
                {
                    if (!decimal.TryParse(POAmountTextBox.Text, out poAmount) || poAmount < 0)
                    {
                        MessageBox.Show("قيمة PO يجب أن تكون رقم صحيح أكبر من أو يساوي صفر.", "خطأ في التحقق",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Get status mapping
                var statusOptions = new[] { "نشط", "معلق", "مكتمل", "ملغي" };
                var selectedStatus = StatusComboBox.SelectedIndex >= 0 && StatusComboBox.SelectedIndex < statusOptions.Length
                    ? statusOptions[StatusComboBox.SelectedIndex]
                    : "نشط";

                if (_isEdit && _project != null)
                {
                    _project.Name = NameTextBox.Text.Trim();
                    _project.Description = DescriptionTextBox.Text.Trim();
                    _project.Status = selectedStatus;
                    _project.PODate = PODatePicker.SelectedDate;
                    _project.POAmount = poAmount;

                    await App.DataService.UpdateProjectAsync(_project);
                }
                else
                {
                    var newProject = new Project
                    {
                        Name = NameTextBox.Text.Trim(),
                        Description = DescriptionTextBox.Text.Trim(),
                        Status = selectedStatus,
                        CreatedDate = DateTime.Now,
                        PODate = PODatePicker.SelectedDate,
                        POAmount = poAmount
                    };

                    await App.DataService.AddProjectAsync(newProject);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving project: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
