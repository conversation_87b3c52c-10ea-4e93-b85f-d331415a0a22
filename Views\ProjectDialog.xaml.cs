using System;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;

namespace FinancialTracker
{
    public partial class ProjectDialog : Window
    {
        private Project? _project;
        private bool _isEdit;

        public ProjectDialog(Project? project = null)
        {
            InitializeComponent();
            _project = project;
            _isEdit = project != null;

            if (_isEdit && _project != null)
            {
                Title = "Edit Project";
                NameTextBox.Text = _project.Name;
                DescriptionTextBox.Text = _project.Description;
                PODatePicker.SelectedDate = _project.PODate;
                POAmountTextBox.Text = _project.POAmount > 0 ? _project.POAmount.ToString("F2") : "";
                ManualTasksAmountTextBox.Text = _project.ManualTasksAmount > 0 ? _project.ManualTasksAmount.ToString("F2") : "";
                ManualServicesAmountTextBox.Text = _project.ManualServicesAmount > 0 ? _project.ManualServicesAmount.ToString("F2") : "";

                // Set status - map to English
                var statusMapping = new System.Collections.Generic.Dictionary<string, int>
                {
                    { "Active", 0 }, { "نشط", 0 },
                    { "On Hold", 1 }, { "معلق", 1 },
                    { "Completed", 2 }, { "مكتمل", 2 },
                    { "Cancelled", 3 }, { "ملغي", 3 }
                };

                if (statusMapping.ContainsKey(_project.Status))
                {
                    StatusComboBox.SelectedIndex = statusMapping[_project.Status];
                }
                else
                {
                    StatusComboBox.SelectedIndex = 0; // Default to Active
                }
            }
            else
            {
                Title = "Add New Project";
                StatusComboBox.SelectedIndex = 0; // Default to Active
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(NameTextBox.Text))
                {
                    MessageBox.Show("Project name is required.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Validate PO Amount
                decimal poAmount = 0;
                if (!string.IsNullOrWhiteSpace(POAmountTextBox.Text))
                {
                    if (!decimal.TryParse(POAmountTextBox.Text, out poAmount) || poAmount < 0)
                    {
                        MessageBox.Show("PO value must be a valid number greater than or equal to zero.", "Validation Error",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Validate Manual Tasks Amount
                decimal manualTasksAmount = 0;
                if (!string.IsNullOrWhiteSpace(ManualTasksAmountTextBox.Text))
                {
                    if (!decimal.TryParse(ManualTasksAmountTextBox.Text, out manualTasksAmount) || manualTasksAmount < 0)
                    {
                        MessageBox.Show("Tasks amount must be a valid number greater than or equal to zero.", "Validation Error",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Validate Manual Services Amount
                decimal manualServicesAmount = 0;
                if (!string.IsNullOrWhiteSpace(ManualServicesAmountTextBox.Text))
                {
                    if (!decimal.TryParse(ManualServicesAmountTextBox.Text, out manualServicesAmount) || manualServicesAmount < 0)
                    {
                        MessageBox.Show("Services amount must be a valid number greater than or equal to zero.", "Validation Error",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Get status mapping
                var statusOptions = new[] { "Active", "On Hold", "Completed", "Cancelled" };
                var selectedStatus = StatusComboBox.SelectedIndex >= 0 && StatusComboBox.SelectedIndex < statusOptions.Length
                    ? statusOptions[StatusComboBox.SelectedIndex]
                    : "Active";

                if (_isEdit && _project != null)
                {
                    _project.Name = NameTextBox.Text.Trim();
                    _project.Description = DescriptionTextBox.Text.Trim();
                    _project.Status = selectedStatus;
                    _project.PODate = PODatePicker.SelectedDate;
                    _project.POAmount = poAmount;
                    _project.ManualTasksAmount = manualTasksAmount;
                    _project.ManualServicesAmount = manualServicesAmount;

                    // Update CreatedDate to match PODate if PODate is set
                    if (_project.PODate.HasValue)
                    {
                        _project.CreatedDate = _project.PODate.Value;
                    }

                    await App.DataService.UpdateProjectAsync(_project);
                }
                else
                {
                    var newProject = new Project
                    {
                        Name = NameTextBox.Text.Trim(),
                        Description = DescriptionTextBox.Text.Trim(),
                        Status = selectedStatus,
                        CreatedDate = PODatePicker.SelectedDate ?? DateTime.Now, // Use PO Date as Created Date
                        PODate = PODatePicker.SelectedDate,
                        POAmount = poAmount,
                        ManualTasksAmount = manualTasksAmount,
                        ManualServicesAmount = manualServicesAmount
                    };

                    await App.DataService.AddProjectAsync(newProject);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving project: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
