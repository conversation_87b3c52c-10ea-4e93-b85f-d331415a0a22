<Window x:Class="FinancialTracker.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        Title="Financial Tracker"
        Height="800"
        Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Top Bar -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Finance" Width="32" Height="32" Margin="0,0,8,0"/>
                    <TextBlock Text="Financial Tracker" FontSize="20" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="DashboardButton" Content="Dashboard" Style="{StaticResource MaterialDesignFlatButton}"
                            Foreground="White" Margin="8,0" Click="DashboardButton_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1">
            <Grid Margin="16">
                <!-- Dashboard Content -->
                <Grid x:Name="DashboardContent" Visibility="Visible">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <TextBlock Grid.Row="0" Text="Projects Overview" FontSize="20" FontWeight="Bold" Margin="0,0,0,12"/>

                    <!-- Compact Summary Cards -->
                    <Grid Grid.Row="1" Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="8" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="Total Projects" Opacity="0.7" HorizontalAlignment="Center" TextAlignment="Center" FontSize="10"/>
                                <TextBlock x:Name="TotalProjectsText" Text="5" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="1" Margin="4" Padding="8" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="Total Amount" Opacity="0.7" HorizontalAlignment="Center" TextAlignment="Center" FontSize="10"/>
                                <TextBlock x:Name="TotalAmountText" Text="$0.00" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="2" Margin="4" Padding="8" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="Total Paid" Opacity="0.7" HorizontalAlignment="Center" TextAlignment="Center" FontSize="10"/>
                                <TextBlock x:Name="TotalPaidText" Text="$0.00" FontSize="18" FontWeight="Bold" Foreground="Green" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="3" Margin="4" Padding="8" MinHeight="60" Visibility="Collapsed">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="Total Commitments" Opacity="0.7" HorizontalAlignment="Center" TextAlignment="Center" FontSize="10"/>
                                <TextBlock x:Name="TotalCommitmentsText" Text="0" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>

                    <!-- Compact Commitments Details -->
                    <Grid Grid.Row="2" Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="8" Background="#E3F2FD" MinHeight="70">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="Task Commitments" Opacity="0.7" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="11"/>
                                <TextBlock x:Name="TaskCommitmentsText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#1976D2" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,2"/>
                                <TextBlock x:Name="TaskCommitmentsAmountText" Text="$0.00" FontSize="13" FontWeight="Medium" Opacity="0.9" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="1" Margin="4" Padding="8" Background="#E8F5E8" MinHeight="70">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="Service Commitments" Opacity="0.7" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="11"/>
                                <TextBlock x:Name="ServiceCommitmentsText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#388E3C" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,2"/>
                                <TextBlock x:Name="ServiceCommitmentsAmountText" Text="$0.00" FontSize="13" FontWeight="Medium" Opacity="0.9" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="2" Margin="4" Padding="8" Background="#FFF3E0" MinHeight="70">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="Other Commitments" Opacity="0.7" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="11"/>
                                <TextBlock x:Name="OtherCommitmentsText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#F57C00" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,2"/>
                                <TextBlock x:Name="OtherCommitmentsAmountText" Text="$0.00" FontSize="13" FontWeight="Medium" Opacity="0.9" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>



                    <!-- Projects Details - Takes remaining space -->
                    <materialDesign:Card Grid.Row="3" Margin="4" Padding="12">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <!-- Header -->
                            <Grid Grid.Row="0" Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Financial Projects Summary" FontSize="16" FontWeight="Medium" VerticalAlignment="Center"/>
                                <Button Grid.Column="1" Content="Add New Project" Style="{StaticResource MaterialDesignRaisedButton}"
                                        FontSize="12" Padding="12,6" Click="AddProjectButton_Click"/>
                            </Grid>

                            <!-- Search and Filter Section - Compact -->
                            <materialDesign:Card Grid.Row="1" Margin="0,0,0,8" Padding="0">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16,12">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                                <materialDesign:PackIcon Kind="FilterVariant" Width="20" Height="20" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                                <TextBlock Text="Search and Filter" FontSize="14" FontWeight="Medium" VerticalAlignment="Center"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                <Button x:Name="ClearFiltersButton"
                                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="0,0,8,0" Padding="12,6" FontSize="12"
                                                        Click="ClearFiltersButton_Click">
                                                    <StackPanel Orientation="Horizontal">
                                                        <materialDesign:PackIcon Kind="FilterRemove" Width="16" Height="16" Margin="0,0,4,0"/>
                                                        <TextBlock Text="Clear Filters"/>
                                                    </StackPanel>
                                                </Button>

                                                <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                                                        ToolTip="Refresh Data" Click="RefreshButton_Click">
                                                    <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18"/>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </materialDesign:ColorZone>

                                    <!-- Filter Controls -->
                                    <Grid Grid.Row="1" Margin="16">
                                        <!-- Search Box Only -->
                                        <StackPanel HorizontalAlignment="Center" MaxWidth="600">
                                            <TextBlock Text="Search Projects" FontSize="12" FontWeight="Medium" Margin="0,0,0,4" Opacity="0.8" HorizontalAlignment="Center"/>
                                            <Grid>
                                                <TextBox x:Name="SearchTextBox" materialDesign:HintAssist.Hint="Type project name to search..."
                                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                         Padding="40,8,8,8" MinWidth="400"
                                                         TextChanged="SearchTextBox_TextChanged"/>
                                                <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20"
                                                                       HorizontalAlignment="Left" VerticalAlignment="Center"
                                                                       Margin="12,0,0,0" Opacity="0.6" />
                                            </Grid>
                                        </StackPanel>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>
                            <!-- Projects Table - Takes remaining space -->
                            <DataGrid Grid.Row="2" x:Name="ProjectsSummaryDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal"
                                      HorizontalContentAlignment="Center" VerticalContentAlignment="Center">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Project Name" Binding="{Binding Name}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Total Cost" Binding="{Binding TotalCost, StringFormat='{}{0:C0}'}" Width="*" MinWidth="100">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Total Paid" Binding="{Binding TotalPaid, StringFormat='{}{0:C0}'}" Width="*" MinWidth="100">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Remaining" Binding="{Binding Remaining, StringFormat='{}{0:C0}'}" Width="*" MinWidth="100">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <DataGridTextColumn Header="Commitments Count" Binding="{Binding TotalCommitments}" Width="*" MinWidth="100" Visibility="Collapsed">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="*" MinWidth="70">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTemplateColumn Header="Files" Width="*" MinWidth="80">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                    <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16"
                                                                           Foreground="Green" ToolTip="Contains files"
                                                                           Visibility="{Binding HasFiles, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                                    <TextBlock Text="{Binding FilesCount}" Margin="4,0,0,0" VerticalAlignment="Center"
                                                               Visibility="{Binding HasFiles, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTemplateColumn Header="Actions" Width="2.5*" MinWidth="280">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                    <Button Content="View Details" Style="{StaticResource MaterialDesignRaisedButton}"
                                                            Margin="3" Padding="8,4" FontSize="10" Click="ViewProjectDetailsButton_Click"/>
                                                    <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="3" Padding="8,4" FontSize="10" Click="EditProjectButton_Click"/>
                                                    <Button Content="Delete" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="3" Padding="8,4" FontSize="10" Click="DeleteProjectButton_Click"/>
                                                    <Button Content="Files" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="3" Padding="8,4" FontSize="10" Click="ViewProjectFilesButton_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </materialDesign:Card>
                </Grid>




            </Grid>
        </ScrollViewer>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,4">
            <Grid>
                <TextBlock Text="Ready" VerticalAlignment="Center"/>
                <TextBlock Text="v1.0.0" HorizontalAlignment="Right" VerticalAlignment="Center"/>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>
