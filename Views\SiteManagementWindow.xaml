<Window x:Class="FinancialTracker.Views.SiteManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إدارة المواقع" Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <!-- Custom Styles -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10,0,25"/>
        </Style>

        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="MinHeight" Value="80"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.4" BlurRadius="10"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Margin" Value="8,4"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedAccentButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Margin" Value="8,4"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>
    </Window.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header with Icon -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Background="{DynamicResource MaterialDesignToolBarBackground}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="Domain" Width="32" Height="32"
                                       Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                <TextBlock Text="إدارة المواقع والتقارير" Style="{StaticResource HeaderTextStyle}"
                          Foreground="White" Margin="0"/>
            </StackPanel>
        </Border>

        <!-- Action Buttons -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="btnAddSite"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Click="BtnAddSite_Click">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة موقع جديد"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button Name="btnEditSite"
                        Style="{StaticResource ModernButtonStyle}"
                        Click="BtnEditSite_Click" IsEnabled="False">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تعديل الموقع"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button Name="btnRefresh"
                        Style="{StaticResource ModernButtonStyle}"
                        Click="BtnRefresh_Click">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Separator Width="20" Background="Transparent"/>

                <Button Name="btnGenerateReport"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Click="BtnGenerateReport_Click">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartLine" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تقرير شامل"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <TabControl Name="tabControl" Style="{StaticResource MaterialDesignTabControl}">

                <!-- Sites Tab -->
                <TabItem>
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Domain" Width="20" Height="20" Margin="0,0,8,0"/>
                            <TextBlock Text="المواقع"/>
                        </StackPanel>
                    </TabItem.Header>

                    <Grid Margin="16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="350"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Sites List -->
                        <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,16,0">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                    <materialDesign:PackIcon Kind="FormatListBulleted" Width="24" Height="24"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="قائمة المواقع" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                                </StackPanel>

                                <ListBox Grid.Row="1" Name="lstSites" SelectionChanged="LstSites_SelectionChanged"
                                        Style="{StaticResource MaterialDesignListBox}">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                                   CornerRadius="8" Padding="12" Margin="4">
                                                <StackPanel>
                                                    <TextBlock Text="{Binding DisplayName}" FontWeight="Bold" FontSize="16"/>
                                                    <TextBlock Text="{Binding Name}" FontSize="12" Foreground="Gray" Margin="0,2,0,0"/>
                                                    <TextBlock Text="{Binding Description}" FontSize="12" Margin="0,4,0,0" TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </Border>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>
                            </Grid>
                        </Border>

                        <!-- Site Details -->
                        <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                    <materialDesign:PackIcon Kind="Information" Width="24" Height="24"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="تفاصيل الموقع" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                                </StackPanel>

                                <ScrollViewer Grid.Row="1">
                                    <StackPanel Name="pnlSiteDetails" Margin="8">
                                        <Border Background="{DynamicResource MaterialDesignDivider}"
                                               CornerRadius="8" Padding="32" HorizontalAlignment="Center">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="SelectionSearch" Width="48" Height="48"
                                                                       Foreground="Gray" HorizontalAlignment="Center"/>
                                                <TextBlock Text="اختر موقعاً من القائمة لعرض التفاصيل"
                                                          HorizontalAlignment="Center" VerticalAlignment="Center"
                                                          FontSize="14" Foreground="Gray" Margin="0,8,0,0"/>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </ScrollViewer>
                            </Grid>
                        </Border>
                    </Grid>
                </TabItem>

                <!-- Site Reports Tab -->
                <TabItem>
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartBar" Width="20" Height="20" Margin="0,0,8,0"/>
                            <TextBlock Text="تقارير المواقع"/>
                        </StackPanel>
                    </TabItem.Header>

                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Report Controls -->
                        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,16">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="Filter" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="اختر الموقع:" VerticalAlignment="Center" Margin="0,0,16,0" FontWeight="Medium"/>
                                <ComboBox Name="cmbSiteFilter" Width="250" Margin="0,0,16,0"
                                          Style="{StaticResource MaterialDesignComboBox}"
                                          SelectionChanged="CmbSiteFilter_SelectionChanged">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding DisplayName}"/>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                                <Button Name="btnGenerateSiteReport"
                                        Style="{StaticResource PrimaryButtonStyle}"
                                        Click="BtnGenerateSiteReport_Click">
                                    <Button.Content>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FileChart" Width="18" Height="18" Margin="0,0,8,0"/>
                                            <TextBlock Text="إنشاء التقرير"/>
                                        </StackPanel>
                                    </Button.Content>
                                </Button>
                            </StackPanel>
                        </Border>

                        <!-- Report Content -->
                        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
                            <ScrollViewer>
                                <StackPanel Name="pnlReportContent" Margin="16">
                                    <Border Background="{DynamicResource MaterialDesignDivider}"
                                           CornerRadius="8" Padding="32" HorizontalAlignment="Center">
                                        <StackPanel HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="ChartAreaspline" Width="48" Height="48"
                                                                   Foreground="Gray" HorizontalAlignment="Center"/>
                                            <TextBlock Text="اختر موقعاً وانقر على 'إنشاء التقرير' لعرض التفاصيل"
                                                      HorizontalAlignment="Center" VerticalAlignment="Center"
                                                      FontSize="14" Foreground="Gray" Margin="0,8,0,0"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </ScrollViewer>
                        </Border>
                    </Grid>
                </TabItem>

                <!-- Multi-Site Comparison Tab -->
                <TabItem>
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Compare" Width="20" Height="20" Margin="0,0,8,0"/>
                            <TextBlock Text="مقارنة المواقع"/>
                        </StackPanel>
                    </TabItem.Header>

                    <Border Style="{StaticResource CardStyle}" Margin="16">
                        <ScrollViewer>
                            <StackPanel Name="pnlMultiSiteReport" Margin="16">
                                <Button Name="btnGenerateMultiSiteReport"
                                        Style="{StaticResource PrimaryButtonStyle}"
                                        Width="300" Height="50" HorizontalAlignment="Center" Margin="0,0,0,32"
                                        Click="BtnGenerateMultiSiteReport_Click">
                                    <Button.Content>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ChartMultiple" Width="24" Height="24" Margin="0,0,12,0"/>
                                            <TextBlock Text="إنشاء تقرير مقارنة شامل" FontSize="16"/>
                                        </StackPanel>
                                    </Button.Content>
                                </Button>

                                <Border Background="{DynamicResource MaterialDesignDivider}"
                                       CornerRadius="8" Padding="32" HorizontalAlignment="Center">
                                    <StackPanel HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="ChartMultiple" Width="48" Height="48"
                                                               Foreground="Gray" HorizontalAlignment="Center"/>
                                        <TextBlock Text="انقر على الزر أعلاه لإنشاء تقرير مقارنة شامل بين جميع المواقع"
                                                  HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  FontSize="14" Foreground="Gray" Margin="0,8,0,0" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </ScrollViewer>
                    </Border>
                </TabItem>

            </TabControl>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}" Background="{DynamicResource MaterialDesignToolBarBackground}">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="Information" Width="16" Height="16"
                                       Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Name="txtStatus" Text="جاهز" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>