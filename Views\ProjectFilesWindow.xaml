<Window x:Class="FinancialTracker.Views.ProjectFilesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إدارة ملفات المشروع"
        Height="600"
        Width="900"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FolderOpen" Width="24" Height="24" Margin="0,0,8,0"/>
                    <TextBlock x:Name="ProjectNameText" Text="ملفات المشروع" FontSize="18" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Content="تحديث" Style="{StaticResource MaterialDesignFlatButton}" Foreground="White" Margin="8,0" Click="RefreshButton_Click"/>
                    <Button Content="إضافة ملف" Style="{StaticResource MaterialDesignFlatButton}" Foreground="White" Margin="8,0" Click="AddFileButton_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="16">
            <StackPanel>
                <!-- Summary Cards -->
                <Grid Margin="0,0,0,16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- First Row - File Counts -->
                    <Grid Grid.Row="0" Margin="0,0,0,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="12" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="إجمالي الملفات" Opacity="0.7" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="TotalFilesText" Text="0" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>



                        <materialDesign:Card Grid.Column="2" Margin="4" Padding="12" Background="#FFF3E0" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="ملفات الارتباطات" Opacity="0.7" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="CommitmentFilesText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#F57C00" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="3" Margin="4" Padding="12" Background="#E8F5E8" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="ملفات الفواتير" Opacity="0.7" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="InvoiceFilesText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#388E3C" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>

                    <!-- Second Row - Size Info -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="12" MinHeight="50">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="الحجم الإجمالي للملفات" Opacity="0.7" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="TotalSizeText" Text="0 MB" FontSize="16" FontWeight="Bold" Foreground="Orange" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </Grid>

                <!-- Files List -->
                <materialDesign:Card Padding="16">
                    <StackPanel>
                        <TextBlock Text="قائمة الملفات" FontSize="16" FontWeight="Medium" Margin="0,0,0,16"/>
                        
                        <!-- Search Box -->
                        <TextBox x:Name="FileSearchTextBox" materialDesign:HintAssist.Hint="🔍 البحث في الملفات..." 
                                 Margin="0,0,0,16" TextChanged="FileSearchTextBox_TextChanged"/>

                        <DataGrid x:Name="FilesDataGrid" AutoGenerateColumns="False" CanUserAddRows="False" 
                                  IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal" MaxHeight="300">
                            <DataGrid.Columns>
                                <DataGridTemplateColumn Header="النوع" Width="60">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20" 
                                                                   Foreground="{Binding FileTypeColor}"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTextColumn Header="اسم الملف" Binding="{Binding FileName}" Width="200"/>
                                <DataGridTextColumn Header="المصدر" Binding="{Binding Source}" Width="100"/>
                                <DataGridTextColumn Header="الحجم" Binding="{Binding FileSizeFormatted}" Width="80"/>
                                <DataGridTextColumn Header="تاريخ الإضافة" Binding="{Binding DateAdded, StringFormat='{}{0:yyyy-MM-dd}'}" Width="120"/>
                                <DataGridTemplateColumn Header="الإجراءات" Width="200">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <Button Content="فتح" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="2" Padding="6,3" FontSize="11" Click="OpenFileButton_Click"/>
                                                <Button Content="نسخ المسار" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="2" Padding="6,3" FontSize="11" Click="CopyPathButton_Click"/>
                                                <Button Content="حذف" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="2" Padding="6,3" FontSize="11" Click="DeleteFileButton_Click"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,4">
            <Grid>
                <TextBlock x:Name="StatusText" Text="جاهز" VerticalAlignment="Center"/>
                <Button Content="إغلاق" HorizontalAlignment="Right" Style="{StaticResource MaterialDesignFlatButton}" Click="CloseButton_Click"/>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>
