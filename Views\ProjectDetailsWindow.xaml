<Window x:Class="FinancialTracker.ProjectDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        Title="Project Details" 
        Height="900" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FolderOpen" Width="32" Height="32" Margin="0,0,8,0" Foreground="White"/>
                    <TextBlock x:Name="ProjectNameText" Text="Project Details" FontSize="20" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Content="Edit Project" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Foreground="White" Margin="8,0" Click="EditProjectButton_Click"/>
                    <Button Content="Close" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Foreground="White" Margin="8,0" Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

                <!-- Project Info Card - Enhanced with PO Info -->
                <materialDesign:Card Grid.Row="0" Margin="0,0,0,8" Padding="12">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- First Row - Basic Info -->
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="Project Name" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                                <TextBlock x:Name="ProjectNameDetail" Text="" FontSize="14" FontWeight="Medium"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="0,0,8,0">
                                <TextBlock Text="Status" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                                <TextBlock x:Name="ProjectStatusDetail" Text="" FontSize="14"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Margin="0,0,8,0">
                                <TextBlock Text="Created Date" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                                <TextBlock x:Name="ProjectCreatedDetail" Text="" FontSize="14"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3">
                                <TextBlock Text="Description" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                                <TextBlock x:Name="ProjectDescriptionDetail" Text="" FontSize="14" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Grid>

                        <!-- Second Row - PO Information -->
                        <Grid Grid.Row="1" Margin="0,8,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="PO Date (Project Start)" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                                <TextBlock x:Name="PODateDetail" Text="Not Set" FontSize="14" FontWeight="Medium" Foreground="#1976D2"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="0,0,8,0">
                                <TextBlock Text="Full PO Value" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                                <TextBlock x:Name="POAmountDetail" Text="$0.00" FontSize="14" FontWeight="Bold" Foreground="#1976D2"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Margin="0,0,8,0">
                                <TextBlock Text="Spent from PO" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                                <TextBlock x:Name="SpentFromPODetail" Text="$0.00" FontSize="14" FontWeight="Medium" Foreground="#F57C00"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3">
                                <TextBlock Text="Remaining from PO" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                                <TextBlock x:Name="RemainingFromPODetail" Text="$0.00" FontSize="14" FontWeight="Bold" Foreground="#388E3C"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </materialDesign:Card>



                <!-- Tabs -->
                <TabControl Grid.Row="2" Style="{StaticResource MaterialDesignTabControl}">
                    <!-- Financial Summary Tab -->
                    <TabItem Header="Financial Summary">
                        <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="0,16,0,0">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                            <!-- PO Financial Summary -->
                            <materialDesign:Card Grid.Row="0" Margin="0,0,0,16" Padding="16" Background="#E8F5E8">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- PO Header -->
                                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,12">
                                        <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20" Foreground="#2E7D32" Margin="0,0,8,0"/>
                                        <TextBlock Text="Purchase Order (PO) Summary" FontSize="16" FontWeight="Bold" Foreground="#2E7D32"/>
                                    </StackPanel>

                                    <!-- PO Financial Cards -->
                                    <Grid Grid.Row="1">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="12" Background="#C8E6C9" MinHeight="80">
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <TextBlock Text="Total PO Value" Opacity="0.8" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12"/>
                                                <TextBlock x:Name="POTotalValueText" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#1B5E20" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4"/>
                                                <TextBlock Text="Complete Budget" FontSize="10" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <materialDesign:Card Grid.Column="1" Margin="4" Padding="12" Background="#FFCDD2" MinHeight="80">
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <TextBlock Text="Total Spent" Opacity="0.8" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12"/>
                                                <TextBlock x:Name="POSpentText" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#C62828" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4"/>
                                                <TextBlock Text="From PO Budget" FontSize="10" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <materialDesign:Card Grid.Column="2" Margin="4" Padding="12" Background="#FFF9C4" MinHeight="80">
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <TextBlock Text="Remaining" Opacity="0.8" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12"/>
                                                <TextBlock x:Name="PORemainingText" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#F57F17" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4"/>
                                                <TextBlock Text="Available Budget" FontSize="10" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>

                            <!-- Tasks Financial Summary -->
                            <materialDesign:Card Grid.Row="1" Margin="0,0,0,16" Padding="16" Background="#E3F2FD">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Tasks Header -->
                                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,12">
                                        <materialDesign:PackIcon Kind="Cog" Width="20" Height="20" Foreground="#1976D2" Margin="0,0,8,0"/>
                                        <TextBlock Text="Tasks Summary (Software &amp; Hardware)" FontSize="16" FontWeight="Bold" Foreground="#1976D2"/>
                                    </StackPanel>

                                    <!-- Tasks Financial Cards -->
                                    <Grid Grid.Row="1">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="12" Background="#BBDEFB" MinHeight="80">
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <TextBlock Text="Total Tasks Value" Opacity="0.8" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12"/>
                                                <TextBlock x:Name="TasksTotalValueText" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#0D47A1" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4"/>
                                                <TextBlock Text="Allocated Budget" FontSize="10" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <materialDesign:Card Grid.Column="1" Margin="4" Padding="12" Background="#FFCDD2" MinHeight="80">
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <TextBlock Text="Tasks Spent" Opacity="0.8" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12"/>
                                                <TextBlock x:Name="TasksSpentText" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#C62828" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4"/>
                                                <TextBlock Text="Used from Tasks" FontSize="10" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <materialDesign:Card Grid.Column="2" Margin="4" Padding="12" Background="#FFF9C4" MinHeight="80">
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <TextBlock Text="Tasks Remaining" Opacity="0.8" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12"/>
                                                <TextBlock x:Name="TasksRemainingText" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#F57F17" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4"/>
                                                <TextBlock Text="Available for Tasks" FontSize="10" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>

                            <!-- Services Financial Summary -->
                            <materialDesign:Card Grid.Row="2" Margin="0,0,0,16" Padding="16" Background="#FFF3E0">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Services Header -->
                                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,12">
                                        <materialDesign:PackIcon Kind="HandHeart" Width="20" Height="20" Foreground="#F57C00" Margin="0,0,8,0"/>
                                        <TextBlock Text="Services Summary" FontSize="16" FontWeight="Bold" Foreground="#F57C00"/>
                                    </StackPanel>

                                    <!-- Services Financial Cards -->
                                    <Grid Grid.Row="1">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="12" Background="#FFE0B2" MinHeight="80">
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <TextBlock Text="Total Services Value" Opacity="0.8" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12"/>
                                                <TextBlock x:Name="ServicesTotalValueText" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#E65100" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4"/>
                                                <TextBlock Text="Allocated Budget" FontSize="10" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <materialDesign:Card Grid.Column="1" Margin="4" Padding="12" Background="#FFCDD2" MinHeight="80">
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <TextBlock Text="Services Spent" Opacity="0.8" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12"/>
                                                <TextBlock x:Name="ServicesSpentText" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#C62828" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4"/>
                                                <TextBlock Text="Used from Services" FontSize="10" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>

                                        <materialDesign:Card Grid.Column="2" Margin="4" Padding="12" Background="#FFF9C4" MinHeight="80">
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <TextBlock Text="Services Remaining" Opacity="0.8" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="12"/>
                                                <TextBlock x:Name="ServicesRemainingText" Text="$0" FontSize="20" FontWeight="Bold" Foreground="#F57F17" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,4"/>
                                                <TextBlock Text="Available for Services" FontSize="10" Opacity="0.8" HorizontalAlignment="Center" TextAlignment="Center"/>
                                            </StackPanel>
                                        </materialDesign:Card>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>
                            </Grid>
                        </ScrollViewer>
                    </TabItem>

                    <!-- Invoices Tab -->
                    <TabItem Header="Invoices">
                        <Grid Margin="0,16,0,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Invoice Controls -->
                            <materialDesign:Card Grid.Row="0" Margin="0,0,0,16" Padding="0">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="12,8">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                                <materialDesign:PackIcon Kind="FileDocument" Width="18" Height="18" Margin="0,0,6,0"/>
                                                <TextBlock Text="Filter Invoices" FontSize="13" FontWeight="Medium"/>
                                            </StackPanel>

                                            <Button Grid.Column="2" Content="Add New Invoice" Style="{StaticResource MaterialDesignRaisedButton}"
                                                    FontSize="12" Padding="12,6" Click="AddInvoiceButton_Click"/>
                                        </Grid>
                                    </materialDesign:ColorZone>

                                    <!-- Filter Controls -->
                                    <Grid Grid.Row="1" Margin="12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                            <TextBlock Text="Search Invoices" FontSize="11" FontWeight="Medium" Margin="0,0,0,4" Opacity="0.8"/>
                                            <Grid>
                                                <TextBox x:Name="InvoiceSearchTextBox" materialDesign:HintAssist.Hint="Invoice number or description..."
                                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                         Padding="35,8,8,8" MinWidth="300"
                                                         TextChanged="InvoiceSearchTextBox_TextChanged"/>
                                                <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16"
                                                                       HorizontalAlignment="Left" VerticalAlignment="Center"
                                                                       Margin="10,0,0,0" Opacity="0.6"/>
                                            </Grid>
                                        </StackPanel>

                                        <Button Grid.Column="1" x:Name="ClearInvoiceFilterButton"
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                Padding="12,8" FontSize="11" VerticalAlignment="Bottom"
                                                Click="ClearInvoiceFilterButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="FilterRemove" Width="14" Height="14" Margin="0,0,4,0"/>
                                                <TextBlock Text="Clear"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>

                            <DataGrid Grid.Row="2" x:Name="InvoicesDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Invoice Number" Binding="{Binding InvoiceNumber}" Width="*"/>
                                    <DataGridTextColumn Header="Type" Binding="{Binding TypeDisplay}" Width="*"/>
                                    <DataGridTextColumn Header="Amount" Binding="{Binding AmountUSD, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="Paid" Binding="{Binding PaidAmount, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="Remaining" Binding="{Binding RemainingAmount, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="Payment Status" Binding="{Binding PaymentStatus}" Width="*"/>
                                    <DataGridTextColumn Header="Signature Status" Binding="{Binding SignatureStatus}" Width="*"/>
                                    <DataGridTextColumn Header="Invoice Date" Binding="{Binding InvoiceDate, StringFormat='{}{0:yyyy-MM-dd}'}" Width="*"/>
                                    <DataGridTextColumn Header="Signature Date" Binding="{Binding SignatureDate, StringFormat='{}{0:yyyy-MM-dd}'}" Width="*"/>
                                    <DataGridTextColumn Header="Commitment" Binding="{Binding Commitment.Title}" Width="*"/>
                                    <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*"/>
                                    <DataGridTemplateColumn Header="Actions" Width="250">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="EditInvoiceButton_Click"/>
                                                    <Button Content="Copy" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="CopyInvoiceButton_Click"/>
                                                    <Button Content="Letter" Style="{StaticResource MaterialDesignRaisedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="OpenInvoiceLetterButton_Click"
                                                            IsEnabled="{Binding LetterFileName, Converter={StaticResource StringToVisibilityConverter}}"/>
                                                    <Button Content="File" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="OpenInvoiceFileButton_Click"
                                                            IsEnabled="{Binding AttachedFileName, Converter={StaticResource StringToVisibilityConverter}}"/>
                                                    <Button Content="Delete" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="DeleteInvoiceButton_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>

                    <!-- Commitments Tab -->
                    <TabItem Header="Commitments">
                        <Grid Margin="0,16,0,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Commitment Controls -->
                            <materialDesign:Card Grid.Row="0" Margin="0,0,0,16" Padding="0">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="12,8">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                                <materialDesign:PackIcon Kind="Link" Width="18" Height="18" Margin="0,0,6,0"/>
                                                <TextBlock Text="Filter Commitments" FontSize="13" FontWeight="Medium"/>
                                            </StackPanel>

                                            <Button Grid.Column="2" Content="Add New Commitment" Style="{StaticResource MaterialDesignRaisedButton}"
                                                    FontSize="12" Padding="12,6" Click="AddCommitmentButton_Click"/>
                                        </Grid>
                                    </materialDesign:ColorZone>

                                    <!-- Filter Controls -->
                                    <Grid Grid.Row="1" Margin="12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                            <TextBlock Text="Search Commitments" FontSize="11" FontWeight="Medium" Margin="0,0,0,4" Opacity="0.8"/>
                                            <Grid>
                                                <TextBox x:Name="CommitmentSearchTextBox" materialDesign:HintAssist.Hint="Commitment title or description..."
                                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                         Padding="35,8,8,8" MinWidth="300"
                                                         TextChanged="CommitmentSearchTextBox_TextChanged"/>
                                                <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16"
                                                                       HorizontalAlignment="Left" VerticalAlignment="Center"
                                                                       Margin="10,0,0,0" Opacity="0.6"/>
                                            </Grid>
                                        </StackPanel>

                                        <Button Grid.Column="1" x:Name="ClearCommitmentFilterButton"
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                Padding="12,8" FontSize="11" VerticalAlignment="Bottom"
                                                Click="ClearCommitmentFilterButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="FilterRemove" Width="14" Height="14" Margin="0,0,4,0"/>
                                                <TextBlock Text="Clear"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>

                            <DataGrid Grid.Row="2" x:Name="CommitmentsDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Title" Binding="{Binding Title}" Width="*"/>
                                    <DataGridTextColumn Header="Type" Binding="{Binding TypeDisplay}" Width="*"/>
                                    <DataGridTextColumn Header="Total Amount" Binding="{Binding AmountUSD, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="Invoiced Amount" Binding="{Binding TotalInvoicedAmount, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="Remaining Amount" Binding="{Binding RemainingAmount, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="Exchange Rate" Binding="{Binding ExchangeRate}" Width="*"/>
                                    <DataGridTextColumn Header="Created Date" Binding="{Binding CreatedDate, StringFormat='{}{0:yyyy-MM-dd}'}" Width="*"/>
                                    <DataGridCheckBoxColumn Header="Active" Binding="{Binding IsActive}" Width="60"/>
                                    <DataGridTextColumn Header="Invoices Count" Binding="{Binding InvoicesCount}" Width="*"/>
                                    <DataGridTemplateColumn Header="Actions" Width="270">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="EditCommitmentButton_Click"/>
                                                    <Button Content="Copy" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="CopyCommitmentButton_Click"/>
                                                    <Button Content="Invoices" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="ViewCommitmentInvoicesButton_Click"/>
                                                    <Button Content="File" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="OpenCommitmentFileButton_Click"
                                                            IsEnabled="{Binding AttachedFileName, Converter={StaticResource StringToVisibilityConverter}}"/>
                                                    <Button Content="Delete" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="DeleteCommitmentButton_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>


                </TabControl>
        </Grid>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,4">
            <Grid>
                <TextBlock x:Name="StatusText" Text="Ready" VerticalAlignment="Center"/>
                <TextBlock Text="Project Details View" HorizontalAlignment="Right" VerticalAlignment="Center"/>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>
