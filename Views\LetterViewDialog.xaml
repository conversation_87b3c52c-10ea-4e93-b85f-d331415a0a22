<Window x:Class="FinancialTracker.Views.LetterViewDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="عرض الخطاب ومرفقاته" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="InfoTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>

        <Style x:Key="LabelTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
        </Style>
    </Window.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Background="{DynamicResource MaterialDesignToolBarBackground}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="EmailOpen" Width="24" Height="24" 
                                       Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Name="txtTitle" Text="عرض الخطاب ومرفقاته" FontSize="20" FontWeight="Bold"
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="8">

                <!-- Letter Basic Info -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Email" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="معلومات الخطاب" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="عنوان الخطاب:" Style="{StaticResource LabelTextStyle}"/>
                                <Border Background="LightBlue" CornerRadius="4">
                                    <TextBlock Name="lblTitle" Style="{StaticResource InfoTextStyle}" FontSize="16"/>
                                </Border>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <TextBlock Text="تاريخ الإرسال:" Style="{StaticResource LabelTextStyle}"/>
                                <Border Background="LightGreen" CornerRadius="4">
                                    <TextBlock Name="lblDateSent" Style="{StaticResource InfoTextStyle}" FontSize="16"/>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Commitment Information -->
                <Border Name="CommitmentSection" Style="{StaticResource CardStyle}" Visibility="Collapsed">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Link" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="معلومات الارتباط" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- First Row - Title -->
                            <StackPanel Grid.Row="0" Margin="0,0,0,8">
                                <TextBlock Text="عنوان الارتباط:" Style="{StaticResource LabelTextStyle}"/>
                                <Border Background="LightBlue" CornerRadius="4">
                                    <TextBlock Name="lblCommitmentTitle" Style="{StaticResource InfoTextStyle}" FontWeight="Bold"/>
                                </Border>
                            </StackPanel>

                            <!-- Second Row - Details -->
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <TextBlock Text="رقم الارتباط:" Style="{StaticResource LabelTextStyle}"/>
                                    <Border Background="LightYellow" CornerRadius="4">
                                        <TextBlock Name="lblCommitmentNumber" Style="{StaticResource InfoTextStyle}"/>
                                    </Border>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="4,0,4,0">
                                    <TextBlock Text="تاريخ الارتباط:" Style="{StaticResource LabelTextStyle}"/>
                                    <Border Background="LightCyan" CornerRadius="4">
                                        <TextBlock Name="lblCommitmentDate" Style="{StaticResource InfoTextStyle}"/>
                                    </Border>
                                </StackPanel>

                                <StackPanel Grid.Column="2" Margin="8,0,0,0">
                                    <TextBlock Text="قيمة الارتباط:" Style="{StaticResource LabelTextStyle}"/>
                                    <Border Background="LightPink" CornerRadius="4">
                                        <TextBlock Name="lblCommitmentAmount" Style="{StaticResource InfoTextStyle}" FontWeight="Bold"/>
                                    </Border>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Invoices Information -->
                <Border Name="InvoicesSection" Style="{StaticResource CardStyle}" Visibility="Collapsed">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Receipt" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="الفواتير المختارة" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- Invoices List -->
                        <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="300">
                            <StackPanel Name="InvoicesContainer">
                                <!-- Invoices will be added dynamically -->
                            </StackPanel>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Certificate Section -->
                <Border Name="CertificateSection" Style="{StaticResource CardStyle}" Visibility="Collapsed">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Certificate" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="ملف الشهادة" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="اسم الملف:" Style="{StaticResource LabelTextStyle}"/>
                                <Border Background="LightGoldenrodYellow" CornerRadius="4">
                                    <TextBlock Name="lblCertificateFileName" Style="{StaticResource InfoTextStyle}"/>
                                </Border>
                            </StackPanel>

                            <Button Grid.Column="1" Name="btnOpenCertificate" Content="فتح الشهادة"
                                   Click="BtnOpenCertificate_Click" Width="100" Height="35"
                                   Margin="16,0,0,0" VerticalAlignment="Bottom"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- All Files Section -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="FileMultiple" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="جميع الملفات المرتبطة" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- Files List -->
                        <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="400">
                            <StackPanel Name="FilesContainer">
                                <!-- Files will be added dynamically -->
                                <TextBlock Text="لا توجد ملفات مختارة" FontSize="12" Foreground="Gray"
                                          HorizontalAlignment="Center" Margin="20" Name="NoFilesMessage"/>
                            </StackPanel>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Notes -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Note" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="ملاحظات" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <TextBlock Name="lblNotes" TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="btnEdit" Content="تعديل" Style="{StaticResource MaterialDesignRaisedAccentButton}"
                        Width="100" Margin="8,0" Click="BtnEdit_Click"/>
                <Button Name="btnClose" Content="إغلاق" Style="{StaticResource MaterialDesignOutlinedButton}"
                        Width="100" Margin="8,0" Click="BtnClose_Click"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
