using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;
using FinancialTracker.Helpers;

namespace FinancialTracker
{
    public partial class InvoiceDialog : Window
    {
        private Invoice? _invoice;
        private bool _isEdit;
        private string _selectedFilePath = string.Empty;
        private string _selectedLetterPath = string.Empty;
        private int? _preSelectedProjectId;
        private List<Commitment> _availableCommitments = new List<Commitment>();
        private int? _preSelectedCommitmentId;
        private List<Site> _availableSites = new List<Site>();

        public InvoiceDialog(Invoice? invoice = null, int? projectId = null, int? commitmentId = null)
        {
            InitializeComponent();
            _invoice = invoice;
            _isEdit = invoice != null;
            _preSelectedProjectId = projectId;
            _preSelectedCommitmentId = commitmentId;

            LoadData();

            // Add event handlers for selection changes
            ProjectComboBox.SelectionChanged += ProjectComboBox_SelectionChanged;
            CommitmentComboBox.SelectionChanged += CommitmentComboBox_SelectionChanged;

            if (_isEdit && _invoice != null)
            {
                Title = "Edit Invoice";
                _ = PopulateFieldsAsync(); // Call async method without awaiting
            }
            else
            {
                Title = "Add New Invoice";
                InvoiceDatePicker.SelectedDate = DateTime.Now;
                TypeComboBox.SelectedIndex = 0; // Default to Task
            }
        }

        private async void ProjectComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            await LoadCommitmentsForProject();
            await LoadSitesForProject();
        }

        private async System.Threading.Tasks.Task LoadSitesForProject()
        {
            try
            {
                var selectedProjectId = ProjectComboBox.SelectedValue as int?;
                if (selectedProjectId.HasValue)
                {
                    using var context = new Data.FinancialContext();
                    var projectSites = context.ProjectSites
                        .Where(ps => ps.ProjectId == selectedProjectId.Value && ps.Status == "Active")
                        .Select(ps => ps.Site)
                        .Where(s => s.Status == "Active")
                        .OrderBy(s => s.Name)
                        .ToList();

                    SiteComboBox.ItemsSource = projectSites;

                    // Select first site if available
                    if (projectSites.Any() && SiteComboBox.SelectedValue == null)
                    {
                        SiteComboBox.SelectedIndex = 0;
                    }
                }
                else
                {
                    SiteComboBox.ItemsSource = _availableSites;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading sites for project: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CommitmentComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            await UpdateTypeFromCommitment();
        }

        private async System.Threading.Tasks.Task UpdateTypeFromCommitment()
        {
            try
            {
                if (CommitmentComboBox.SelectedValue is int commitmentId)
                {
                    var commitment = await App.DataService.GetCommitmentByIdAsync(commitmentId);
                    if (commitment != null)
                    {
                        // حدث نوع الفاتورة ليطابق نوع الارتباط
                        SetTypeFromCommitmentType(commitment.Type);

                        // اجعل حقل النوع للقراءة فقط عندما يكون مرتبط بارتباط
                        TypeComboBox.IsEnabled = false;
                        CustomTypeTextBox.IsEnabled = false;
                    }
                }
                else
                {
                    // إذا لم يتم اختيار ارتباط، اجعل حقل النوع قابل للتعديل
                    TypeComboBox.IsEnabled = true;
                    CustomTypeTextBox.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating type from commitment: {ex.Message}");
            }
        }

        private void SetTypeFromCommitmentType(string commitmentType)
        {
            // ابحث عن النوع في القائمة المنسدلة
            bool typeFound = false;
            foreach (ComboBoxItem item in TypeComboBox.Items)
            {
                if (item.Content?.ToString() == commitmentType)
                {
                    TypeComboBox.SelectedItem = item;
                    CustomTypeTextBox.Visibility = Visibility.Collapsed;
                    CustomTypeTextBox.Text = "";
                    typeFound = true;
                    break;
                }
            }

            // إذا لم يوجد النوع في القائمة، اختر "أخرى" واكتب النوع المخصص
            if (!typeFound)
            {
                foreach (ComboBoxItem item in TypeComboBox.Items)
                {
                    if (item.Content?.ToString() == "أخرى")
                    {
                        TypeComboBox.SelectedItem = item;
                        CustomTypeTextBox.Text = commitmentType;
                        CustomTypeTextBox.Visibility = Visibility.Visible;
                        break;
                    }
                }
            }
        }

        private void TypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                if (selectedItem.Content?.ToString() == "أخرى")
                {
                    CustomTypeTextBox.Visibility = Visibility.Visible;
                }
                else
                {
                    CustomTypeTextBox.Visibility = Visibility.Collapsed;
                    CustomTypeTextBox.Text = "";
                }
            }
        }

        private string GetSelectedType()
        {
            if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var selectedType = selectedItem.Content?.ToString() ?? "مهمات";
                if (selectedType == "أخرى")
                {
                    return !string.IsNullOrWhiteSpace(CustomTypeTextBox.Text) ? CustomTypeTextBox.Text.Trim() : "أخرى";
                }
                return selectedType;
            }
            return "مهمات";
        }

        private async void LoadData()
        {
            try
            {
                var projects = await App.DataService.GetProjectsAsync();
                ProjectComboBox.ItemsSource = projects;

                // Load sites
                await LoadSites();

                // Pre-select project if provided
                if (_preSelectedProjectId.HasValue)
                {
                    ProjectComboBox.SelectedValue = _preSelectedProjectId.Value;
                }

                // Load commitments for the selected project
                await LoadCommitmentsForProject();

                // Pre-select commitment if provided (after commitments are loaded)
                if (_preSelectedCommitmentId.HasValue)
                {
                    CommitmentComboBox.SelectedValue = _preSelectedCommitmentId.Value;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadSites()
        {
            try
            {
                using var context = new Data.FinancialContext();
                _availableSites = context.Sites.Where(s => s.Status == "Active").OrderBy(s => s.Name).ToList();
                SiteComboBox.ItemsSource = _availableSites;

                // Select first site by default if not editing
                if (!_isEdit && _availableSites.Any())
                {
                    SiteComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading sites: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadCommitmentsForProject()
        {
            try
            {
                var selectedProjectId = ProjectComboBox.SelectedValue as int?;
                if (selectedProjectId.HasValue)
                {
                    var allCommitments = await App.DataService.GetCommitmentsAsync();
                    var projectCommitments = allCommitments.Where(c => c.ProjectId == selectedProjectId.Value).ToList();
                    CommitmentComboBox.ItemsSource = projectCommitments;
                }
                else
                {
                    CommitmentComboBox.ItemsSource = null;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading commitments: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task PopulateFieldsAsync()
        {
            if (_invoice == null) return;

            InvoiceNumberTextBox.Text = _invoice.InvoiceNumber;
            ProjectComboBox.SelectedValue = _invoice.ProjectId;
            SiteComboBox.SelectedValue = _invoice.SiteId;

            // Load commitments first, then set the selected commitment
            await LoadCommitmentsForProject();
            CommitmentComboBox.SelectedValue = _invoice.CommitmentId;

            AmountTextBox.Text = _invoice.AmountUSD.ToString("F0");
            ExchangeRateTextBox.Text = _invoice.ExchangeRate.ToString("F4");
            PaidAmountTextBox.Text = _invoice.PaidAmount.ToString("F0");
            InvoiceDatePicker.SelectedDate = _invoice.InvoiceDate;
            SignatureDatePicker.SelectedDate = _invoice.SignatureDate;
            DescriptionTextBox.Text = _invoice.Description;

            await PopulateTypeFields();
        }

        private async System.Threading.Tasks.Task PopulateTypeFields()
        {
            if (_invoice == null) return;

            // إذا كانت الفاتورة مرتبطة بارتباط، حدث النوع من الارتباط
            if (_invoice.CommitmentId.HasValue)
            {
                await UpdateTypeFromCommitment();
            }
            else
            {
                // إذا لم تكن مرتبطة بارتباط، اعرض النوع المحفوظ
                SetTypeFromInvoiceType(_invoice.Type);
                TypeComboBox.IsEnabled = true;
                CustomTypeTextBox.IsEnabled = true;
            }

            if (!string.IsNullOrEmpty(_invoice.AttachedFileName))
            {
                AttachedFileTextBox.Text = _invoice.AttachedFileName;
            }

            // Load letter information
            if (!string.IsNullOrEmpty(_invoice.LetterFileName))
            {
                LetterFileTextBox.Text = _invoice.LetterFileName;
                ViewLetterButton.Visibility = Visibility.Visible;
            }
        }

        private void SetTypeFromInvoiceType(string invoiceType)
        {
            // Set type
            bool typeFound = false;
            foreach (ComboBoxItem item in TypeComboBox.Items)
            {
                if (item.Content?.ToString() == invoiceType)
                {
                    TypeComboBox.SelectedItem = item;
                    typeFound = true;
                    break;
                }
            }

            // If type not found in predefined options, select "أخرى" and set custom text
            if (!typeFound && !string.IsNullOrEmpty(invoiceType))
            {
                foreach (ComboBoxItem item in TypeComboBox.Items)
                {
                    if (item.Content?.ToString() == "أخرى")
                    {
                        TypeComboBox.SelectedItem = item;
                        CustomTypeTextBox.Text = invoiceType;
                        CustomTypeTextBox.Visibility = Visibility.Visible;
                        break;
                    }
                }
            }
        }

        private void SelectFileButton_Click(object sender, RoutedEventArgs e)
        {
            var filter = "PDF Files (*.pdf)|*.pdf|Word Documents (*.doc;*.docx)|*.doc;*.docx|All Files (*.*)|*.*";
            _selectedFilePath = App.FileService.SelectFile(filter);

            if (!string.IsNullOrEmpty(_selectedFilePath))
            {
                AttachedFileTextBox.Text = System.IO.Path.GetFileName(_selectedFilePath);
            }
        }

        private void SelectLetterButton_Click(object sender, RoutedEventArgs e)
        {
            var filter = "PDF Files (*.pdf)|*.pdf|Image Files (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|Word Documents (*.doc;*.docx)|*.doc;*.docx|All Files (*.*)|*.*";
            _selectedLetterPath = App.FileService.SelectFile(filter);

            if (!string.IsNullOrEmpty(_selectedLetterPath))
            {
                LetterFileTextBox.Text = System.IO.Path.GetFileName(_selectedLetterPath);
                ViewLetterButton.Visibility = Visibility.Visible;
            }
        }

        private void ViewLetterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string letterPath = string.Empty;

                // If editing and letter exists, use the existing letter path
                if (_isEdit && _invoice != null && !string.IsNullOrEmpty(_invoice.LetterFilePath))
                {
                    letterPath = _invoice.LetterFilePath;
                }
                // If new letter selected, use the selected path
                else if (!string.IsNullOrEmpty(_selectedLetterPath))
                {
                    letterPath = _selectedLetterPath;
                }

                if (!string.IsNullOrEmpty(letterPath) && System.IO.File.Exists(letterPath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = letterPath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("لا يمكن العثور على ملف الخطاب.", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الخطاب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
                {
                    MessageBox.Show("Invoice number is required.", "Validation Error", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (ProjectComboBox.SelectedValue == null)
                {
                    MessageBox.Show("Please select a project.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (SiteComboBox.SelectedValue == null)
                {
                    MessageBox.Show("Please select a site.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(AmountTextBox.Text, out decimal amount))
                {
                    MessageBox.Show("Please enter a valid amount.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(ExchangeRateTextBox.Text, out decimal exchangeRate))
                {
                    MessageBox.Show("Please enter a valid exchange rate.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تطبيق التقريب المخصص على المبلغ
                amount = MathHelper.RoundCustom(amount);

                if (!decimal.TryParse(PaidAmountTextBox.Text, out decimal paidAmount) || paidAmount < 0)
                {
                    paidAmount = 0;
                }

                // تطبيق التقريب المخصص على المبلغ المدفوع
                paidAmount = MathHelper.RoundCustom(paidAmount);

                if (paidAmount > amount)
                {
                    MessageBox.Show("Paid amount cannot be greater than total amount.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Check if commitment has amount (if commitment is selected)
                if (CommitmentComboBox.SelectedValue is int commitmentId)
                {
                    try
                    {
                        var commitment = await App.DataService.GetCommitmentByIdAsync(commitmentId);
                        if (commitment != null && commitment.AmountUSD <= 0)
                        {
                            MessageBox.Show("لا يمكن إضافة فاتورة لهذا الارتباط لأنه لا يحتوي على مبلغ مالي.", "تنبيه",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في التحقق من الارتباط: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }

                string? attachedFilePath = null;
                string? attachedFileName = null;
                string? letterFilePath = null;
                string? letterFileName = null;

                // Handle file attachment
                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    attachedFileName = System.IO.Path.GetFileName(_selectedFilePath);
                    attachedFilePath = await App.FileService.SaveFileAsync(_selectedFilePath, "invoice", attachedFileName);
                }

                // Handle letter attachment
                if (!string.IsNullOrEmpty(_selectedLetterPath))
                {
                    letterFileName = System.IO.Path.GetFileName(_selectedLetterPath);
                    letterFilePath = await App.FileService.SaveFileAsync(_selectedLetterPath, "letters", letterFileName);
                }

                if (_isEdit && _invoice != null)
                {
                    _invoice.InvoiceNumber = InvoiceNumberTextBox.Text.Trim();
                    _invoice.ProjectId = (int)ProjectComboBox.SelectedValue;
                    _invoice.SiteId = (int)SiteComboBox.SelectedValue;
                    _invoice.CommitmentId = CommitmentComboBox.SelectedValue as int?;
                    _invoice.AmountUSD = amount;
                    _invoice.ExchangeRate = exchangeRate;
                    _invoice.PaidAmount = paidAmount;
                    _invoice.InvoiceDate = InvoiceDatePicker.SelectedDate ?? DateTime.Now;
                    _invoice.SignatureDate = SignatureDatePicker.SelectedDate;
                    _invoice.Description = DescriptionTextBox.Text.Trim();
                    _invoice.IsPaid = paidAmount >= amount; // Auto-set based on paid amount
                    _invoice.PaidDate = paidAmount > 0 ? DateTime.Now : null;

                    // Set type
                    if (TypeComboBox.SelectedItem is ComboBoxItem selectedTypeItem)
                    {
                        var selectedType = selectedTypeItem.Content?.ToString() ?? "مهمات";
                        if (selectedType == "أخرى")
                        {
                            _invoice.Type = !string.IsNullOrWhiteSpace(CustomTypeTextBox.Text) ? CustomTypeTextBox.Text.Trim() : "أخرى";
                        }
                        else
                        {
                            _invoice.Type = selectedType;
                        }
                    }
                    
                    if (attachedFilePath != null)
                    {
                        _invoice.AttachedFilePath = attachedFilePath;
                        _invoice.AttachedFileName = attachedFileName;
                    }

                    if (letterFilePath != null)
                    {
                        _invoice.LetterFilePath = letterFilePath;
                        _invoice.LetterFileName = letterFileName;
                    }
                    
                    await App.DataService.UpdateInvoiceAsync(_invoice);
                }
                else
                {
                    var newInvoice = new Invoice
                    {
                        InvoiceNumber = InvoiceNumberTextBox.Text.Trim(),
                        ProjectId = (int)ProjectComboBox.SelectedValue,
                        SiteId = (int)SiteComboBox.SelectedValue,
                        CommitmentId = CommitmentComboBox.SelectedValue as int?,
                        AmountUSD = amount,
                        ExchangeRate = exchangeRate,
                        PaidAmount = paidAmount,
                        InvoiceDate = InvoiceDatePicker.SelectedDate ?? DateTime.Now,
                        SignatureDate = SignatureDatePicker.SelectedDate,
                        Description = DescriptionTextBox.Text.Trim(),
                        IsPaid = paidAmount >= amount, // Auto-set based on paid amount
                        PaidDate = paidAmount > 0 ? DateTime.Now : null,
                        Type = GetSelectedType(),
                        AttachedFilePath = attachedFilePath,
                        AttachedFileName = attachedFileName,
                        LetterFilePath = letterFilePath,
                        LetterFileName = letterFileName,
                        CreatedDate = DateTime.Now
                    };
                    
                    await App.DataService.AddInvoiceAsync(newInvoice);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving invoice: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}