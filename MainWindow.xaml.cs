using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using FinancialTracker.Models;
using FinancialTracker.Views;
using FinancialTracker.Services;

namespace FinancialTracker
{
    public partial class MainWindow : Window
    {
        private List<ProjectSummary> _allProjects = new List<ProjectSummary>();
        private List<ProjectSummary> _filteredProjects = new List<ProjectSummary>();

        public MainWindow()
        {
            InitializeComponent();
            LoadDashboardData();
        }

        private async void LoadDashboardData()
        {
            try
            {
                var dashboardData = await App.DataService.GetDashboardDataAsync();
                var projectsSummary = await App.DataService.GetProjectsSummaryAsync();

                // التحقق من وجود البيانات
                if (dashboardData == null)
                {
                    MessageBox.Show("لا يمكن تحميل بيانات لوحة المعلومات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // البيانات الأساسية - التحقق من وجود العناصر
                if (TotalProjectsText != null) TotalProjectsText.Text = dashboardData.TotalProjects.ToString();
                if (TotalAmountText != null) TotalAmountText.Text = $"${dashboardData.TotalInvoiceAmount:N2}";
                if (TotalPaidText != null) TotalPaidText.Text = $"${dashboardData.TotalPaidAmount:N2}";
                if (TotalCommitmentsText != null) TotalCommitmentsText.Text = dashboardData.TotalCommitments.ToString();

                // إحصائيات الارتباطات المفصلة
                if (TaskCommitmentsText != null) TaskCommitmentsText.Text = dashboardData.TaskCommitments.ToString();
                if (TaskCommitmentsAmountText != null) TaskCommitmentsAmountText.Text = $"${dashboardData.TaskCommitmentsAmount:N2}";

                if (ServiceCommitmentsText != null) ServiceCommitmentsText.Text = dashboardData.ServiceCommitments.ToString();
                if (ServiceCommitmentsAmountText != null) ServiceCommitmentsAmountText.Text = $"${dashboardData.ServiceCommitmentsAmount:N2}";

                if (OtherCommitmentsText != null) OtherCommitmentsText.Text = dashboardData.OtherCommitments.ToString();
                if (OtherCommitmentsAmountText != null) OtherCommitmentsAmountText.Text = $"${dashboardData.OtherCommitmentsAmount:N2}";

                // حفظ البيانات للفلترة
                _allProjects = projectsSummary?.ToList() ?? new List<ProjectSummary>();
                _filteredProjects = _allProjects.ToList();
                if (ProjectsSummaryDataGrid != null)
                    ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;


            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات لوحة المعلومات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // تهيئة البيانات الفارغة لتجنب الأخطاء
                _allProjects = new List<ProjectSummary>();
                _filteredProjects = new List<ProjectSummary>();
                if (ProjectsSummaryDataGrid != null)
                    ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
            }
        }

        // Navigation event handlers
        private void DashboardButton_Click(object sender, RoutedEventArgs e) => LoadDashboardData();

        private void SiteManagementButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var siteManagementWindow = new Views.SiteManagementWindow();
                siteManagementWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إدارة المواقع: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Project management event handlers
        private void AddProjectButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProjectDialog();
            if (dialog.ShowDialog() == true)
            {
                LoadDashboardData();
            }
        }

        private void ViewProjectDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            // Handle ProjectsSummaryDataGrid
            ProjectSummary? projectSummary = null;

            if (ProjectsSummaryDataGrid?.SelectedItem is ProjectSummary summary)
            {
                projectSummary = summary;
            }
            else
            {
                // Try to get from button's DataContext
                if (sender is Button button && button.DataContext is ProjectSummary summaryFromButton)
                {
                    projectSummary = summaryFromButton;
                }
            }

            int projectId = projectSummary?.Id ?? 0;

            if (projectId > 0)
            {
                var detailsWindow = new ProjectDetailsWindow(projectId);
                detailsWindow.Show();
            }
        }

        private async void EditProjectButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectSummary projectSummary)
            {
                try
                {
                    // Get the full project object
                    var project = await App.DataService.GetProjectByIdAsync(projectSummary.Id);
                    if (project != null)
                    {
                        var dialog = new ProjectDialog(project);
                        if (dialog.ShowDialog() == true)
                        {
                            LoadDashboardData();
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل بيانات المشروع: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void DeleteProjectButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectSummary projectSummary)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف المشروع '{projectSummary.Name}'؟\n\n⚠️ تحذير: سيتم حذف جميع البيانات التالية نهائياً:\n" +
                    $"• جميع الفواتير المرتبطة بالمشروع في كل المواقع\n" +
                    $"• جميع الالتزامات المرتبطة بالمشروع\n" +
                    $"• جميع الردود والتعليقات\n" +
                    $"• ربط المشروع بالمواقع\n\n" +
                    $"هذا الإجراء لا يمكن التراجع عنه!",
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var deleteResult = await App.DataService.DeleteProjectAsync(projectSummary.Id);
                        if (deleteResult)
                        {
                            LoadDashboardData();
                            MessageBox.Show($"تم حذف المشروع '{projectSummary.Name}' وجميع البيانات المرتبطة به بنجاح",
                                "تم الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("لم يتم العثور على المشروع المطلوب حذفه", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"حدث خطأ أثناء حذف المشروع:\n\n{ex.Message}\n\nيرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.",
                            "خطأ في الحذف", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }



        // وظائف البحث والفلترة
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Search error: {ex.Message}");
            }
        }



        private void ApplyFilters()
        {
            try
            {
                if (_allProjects == null || !_allProjects.Any())
                {
                    _filteredProjects = new List<ProjectSummary>();
                    if (ProjectsSummaryDataGrid != null)
                        ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
                    return;
                }

                var filtered = _allProjects.AsEnumerable();

                // فلترة البحث النصي
                if (SearchTextBox != null && !string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    var searchText = SearchTextBox.Text.ToLower();
                    filtered = filtered.Where(p =>
                        (p.Name?.ToLower().Contains(searchText) ?? false) ||
                        (p.Description?.ToLower().Contains(searchText) ?? false));
                }

                // فلترة الحالة


                _filteredProjects = filtered.ToList();
                if (ProjectsSummaryDataGrid != null)
                    ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Filter error: {ex.Message}");
                // في حالة الخطأ، اعرض جميع المشاريع
                _filteredProjects = _allProjects?.ToList() ?? new List<ProjectSummary>();
                if (ProjectsSummaryDataGrid != null)
                    ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
            }
        }

        private void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SearchTextBox != null) SearchTextBox.Text = "";
                ApplyFilters();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Clear filters error: {ex.Message}");
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadDashboardData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // وظائف إدارة الملفات
        private void ViewProjectFilesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.DataContext is ProjectSummary projectSummary)
                {
                    var filesWindow = new ProjectFilesWindow(projectSummary.Id);
                    filesWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الملفات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


    }

    // Helper converter for enabling buttons based on file existence
    public class StringToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            return !string.IsNullOrEmpty(value as string);
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
