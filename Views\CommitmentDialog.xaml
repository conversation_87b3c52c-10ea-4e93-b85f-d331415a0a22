<Window x:Class="FinancialTracker.CommitmentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Commitment Details" 
        Height="650" 
        Width="600"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="معلومات الارتباط" FontSize="20" FontWeight="Bold" Margin="0,0,0,16"
                   HorizontalAlignment="Center" Foreground="{DynamicResource MaterialDesignBody}"/>

        <!-- Form -->
        <ScrollViewer Grid.Row="1">
            <StackPanel>
                <TextBox x:Name="TitleTextBox"
                         materialDesign:HintAssist.Hint="عنوان الارتباط"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="100"
                         Margin="0,0,0,16"/>

                <ComboBox x:Name="ProjectComboBox"
                          materialDesign:HintAssist.Hint="اختر المشروع"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          DisplayMemberPath="Name"
                          SelectedValuePath="Id"
                          SelectionChanged="ProjectComboBox_SelectionChanged"
                          Margin="0,0,0,16"/>

                <ComboBox x:Name="SiteComboBox"
                          materialDesign:HintAssist.Hint="اختر الموقع"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          DisplayMemberPath="DisplayName"
                          SelectedValuePath="Id"
                          Margin="0,0,0,16"/>

                <ComboBox x:Name="TypeComboBox"
                          materialDesign:HintAssist.Hint="نوع الارتباط"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="0,0,0,16"
                          SelectionChanged="TypeComboBox_SelectionChanged">
                    <ComboBoxItem Content="مهمات سوفت وير"/>
                    <ComboBoxItem Content="مهمات هارد وير"/>
                    <ComboBoxItem Content="خدمات"/>
                    <ComboBoxItem Content="أخرى"/>
                </ComboBox>

                <TextBox x:Name="CustomTypeTextBox"
                         materialDesign:HintAssist.Hint="نوع مخصص"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,0,16"
                         Visibility="Collapsed"/>

                <TextBox x:Name="AmountTextBox"
                         materialDesign:HintAssist.Hint="المبلغ (دولار أمريكي)"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,0,16"/>

                <TextBox x:Name="ExchangeRateTextBox"
                         materialDesign:HintAssist.Hint="سعر الصرف (جنيه مصري/دولار)"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Text="1.0"
                         Margin="0,0,0,16"/>

                <DatePicker x:Name="StartDatePicker"
                            materialDesign:HintAssist.Hint="تاريخ البداية (اختياري)"
                            Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                            Margin="0,0,0,16"/>

                <DatePicker x:Name="EndDatePicker"
                            materialDesign:HintAssist.Hint="تاريخ النهاية (اختياري)"
                            Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                            Margin="0,0,0,16"/>

                <TextBox x:Name="DescriptionTextBox"
                         materialDesign:HintAssist.Hint="الوصف"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="500"
                         Height="100"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         Margin="0,0,0,16"/>

                <!-- Created Date Input -->
                <DatePicker x:Name="CreatedDatePicker"
                            materialDesign:HintAssist.Hint="تاريخ الإنشاء"
                            Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                            Margin="0,0,0,16"/>

                <CheckBox x:Name="IsActiveCheckBox"
                          Content="ارتباط نشط"
                          IsChecked="True"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          Margin="0,0,0,16"/>

                <!-- Link Existing Invoices Section -->
                <materialDesign:Card Padding="16" Margin="0,8,0,16">
                    <StackPanel>
                        <TextBlock Text="ربط فواتير موجودة" FontWeight="Medium" Margin="0,0,0,8"/>
                        <TextBlock Text="يمكنك اختيار فواتير موجودة في المشروع وربطها بهذا الارتباط"
                                   FontSize="12" Opacity="0.7" Margin="0,0,0,8"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <ComboBox x:Name="AvailableInvoicesComboBox"
                                      Grid.Column="0"
                                      materialDesign:HintAssist.Hint="اختر فاتورة موجودة..."
                                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                      DisplayMemberPath="InvoiceNumber"/>

                            <Button x:Name="LinkInvoiceButton"
                                    Grid.Column="1"
                                    Content="ربط الفاتورة"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Margin="8,0,0,0"
                                    Click="LinkInvoiceButton_Click"/>

                            <Button x:Name="RefreshInvoicesButton"
                                    Grid.Column="2"
                                    Style="{StaticResource MaterialDesignIconButton}"
                                    ToolTip="تحديث قائمة الفواتير"
                                    Margin="8,0,0,0"
                                    Click="RefreshInvoicesButton_Click">
                                <materialDesign:PackIcon Kind="Refresh"/>
                            </Button>
                        </Grid>

                        <!-- Linked Invoices List -->
                        <TextBlock Text="الفواتير المرتبطة:" FontWeight="Medium" Margin="0,16,0,8"/>
                        <ListBox x:Name="LinkedInvoicesListBox"
                                 MaxHeight="120"
                                 ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                                            <TextBlock Text="{Binding InvoiceNumber}" FontWeight="Medium" Margin="0,0,8,0"/>
                                            <TextBlock Text="-" Margin="0,0,4,0"/>
                                            <TextBlock Text="{Binding AmountUSD, StringFormat='{}{0:C0}'}" Margin="0,0,8,0"/>
                                            <TextBlock Text="{Binding Description}" Opacity="0.7"/>
                                        </StackPanel>

                                        <Button Grid.Column="1"
                                                Content="إلغاء الربط"
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                FontSize="10" Padding="8,4"
                                                Click="UnlinkInvoiceButton_Click"
                                                Tag="{Binding}"/>
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </StackPanel>
                </materialDesign:Card>

                <!-- File Attachment Section -->
                <materialDesign:Card Padding="16" Margin="0,8">
                    <StackPanel>
                        <TextBlock Text="File Attachment" FontWeight="Medium" Margin="0,0,0,8"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox x:Name="AttachedFileTextBox" 
                                     Grid.Column="0"
                                     materialDesign:HintAssist.Hint="No file selected"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     IsReadOnly="True"/>
                            
                            <Button x:Name="SelectFileButton" 
                                    Grid.Column="1"
                                    Content="Browse"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Margin="8,0,0,0"
                                    Click="SelectFileButton_Click"/>
                            
                            <Button x:Name="OpenFileButton" 
                                    Grid.Column="2"
                                    Content="Open"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Margin="8,0,0,0"
                                    IsEnabled="False"
                                    Click="OpenFileButton_Click"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button Content="Cancel"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    Click="CancelButton_Click"/>
            <Button Content="Save"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Padding="16,8"
                    Click="SaveButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
