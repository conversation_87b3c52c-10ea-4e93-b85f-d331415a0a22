using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using FinancialTracker.Models;

namespace FinancialTracker.Views
{
    public partial class ProjectFilesWindow : Window
    {
        private readonly int _projectId;
        private List<ProjectFileInfo> _allFiles = new List<ProjectFileInfo>();
        private List<ProjectFileInfo> _filteredFiles = new List<ProjectFileInfo>();

        public ProjectFilesWindow(int projectId)
        {
            InitializeComponent();
            _projectId = projectId;
            LoadProjectFiles();
        }

        private async void LoadProjectFiles()
        {
            try
            {
                StatusText.Text = "جاري تحميل الملفات...";
                
                // Get project info
                var project = await App.DataService.GetProjectByIdAsync(_projectId);
                if (project != null)
                {
                    ProjectNameText.Text = $"ملفات المشروع: {project.Name}";
                    Title = $"ملفات المشروع - {project.Name}";
                }

                // Get all invoices and commitments for this project
                var invoices = await App.DataService.GetInvoicesAsync();
                var commitments = await App.DataService.GetCommitmentsAsync();

                var projectInvoices = invoices.Where(i => i.ProjectId == _projectId).ToList();
                var projectCommitments = commitments.Where(c => c.ProjectId == _projectId).ToList();

                // Build file list
                _allFiles.Clear();



                // Add invoice files
                foreach (var invoice in projectInvoices.Where(i => !string.IsNullOrEmpty(i.AttachedFileName)))
                {
                    _allFiles.Add(new ProjectFileInfo
                    {
                        FileName = invoice.AttachedFileName,
                        FilePath = invoice.AttachedFilePath,
                        Source = $"فاتورة #{invoice.InvoiceNumber}",
                        DateAdded = invoice.CreatedDate,
                        FileType = GetFileType(invoice.AttachedFileName),
                        FileSize = GetFileSize(invoice.AttachedFilePath)
                    });
                }

                // Add commitment files
                foreach (var commitment in projectCommitments.Where(c => !string.IsNullOrEmpty(c.AttachedFileName)))
                {
                    _allFiles.Add(new ProjectFileInfo
                    {
                        FileName = commitment.AttachedFileName,
                        FilePath = commitment.AttachedFilePath,
                        Source = $"التزام: {commitment.Title}",
                        DateAdded = commitment.CreatedDate,
                        FileType = GetFileType(commitment.AttachedFileName),
                        FileSize = GetFileSize(commitment.AttachedFilePath)
                    });
                }

                // Update UI
                UpdateStatistics();
                _filteredFiles = _allFiles.ToList();
                FilesDataGrid.ItemsSource = _filteredFiles;

                StatusText.Text = $"تم تحميل {_allFiles.Count} ملف";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملفات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "خطأ في التحميل";
            }
        }

        private void UpdateStatistics()
        {
            TotalFilesText.Text = _allFiles.Count.ToString();
            CommitmentFilesText.Text = _allFiles.Count(f => f.Source.Contains("التزام")).ToString();
            InvoiceFilesText.Text = _allFiles.Count(f => f.Source.Contains("فاتورة")).ToString();

            var totalSize = _allFiles.Sum(f => f.FileSize);
            TotalSizeText.Text = $"{totalSize / (1024.0 * 1024.0):F1} MB";
        }

        private string GetFileType(string fileName)
        {
            if (string.IsNullOrEmpty(fileName)) return "Unknown";
            
            var extension = Path.GetExtension(fileName).ToLower();
            return extension switch
            {
                ".pdf" => "PDF",
                ".doc" => "Word",
                ".docx" => "Word",
                ".xls" => "Excel",
                ".xlsx" => "Excel",
                ".txt" => "Text",
                _ => "Other"
            };
        }

        private long GetFileSize(string filePath)
        {
            try
            {
                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    return new FileInfo(filePath).Length;
                }
            }
            catch { }
            return 0;
        }

        private void FileSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFileFilter();
        }

        private void ApplyFileFilter()
        {
            if (_allFiles == null) return;

            var filtered = _allFiles.AsEnumerable();

            if (!string.IsNullOrWhiteSpace(FileSearchTextBox?.Text))
            {
                var searchText = FileSearchTextBox.Text.ToLower();
                filtered = filtered.Where(f => f.FileName.ToLower().Contains(searchText) || 
                                             f.Source.ToLower().Contains(searchText));
            }

            _filteredFiles = filtered.ToList();
            FilesDataGrid.ItemsSource = _filteredFiles;
        }

        private void OpenFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectFileInfo fileInfo)
            {
                try
                {
                    if (!string.IsNullOrEmpty(fileInfo.FilePath) && File.Exists(fileInfo.FilePath))
                    {
                        Process.Start(new ProcessStartInfo(fileInfo.FilePath) { UseShellExecute = true });
                        StatusText.Text = $"تم فتح الملف: {fileInfo.FileName}";
                    }
                    else
                    {
                        MessageBox.Show("الملف غير موجود أو تم حذفه", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CopyPathButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectFileInfo fileInfo)
            {
                try
                {
                    if (!string.IsNullOrEmpty(fileInfo.FilePath))
                    {
                        Clipboard.SetText(fileInfo.FilePath);
                        StatusText.Text = "تم نسخ مسار الملف";
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في نسخ المسار: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DeleteFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectFileInfo fileInfo)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف الملف '{fileInfo.FileName}'؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        if (!string.IsNullOrEmpty(fileInfo.FilePath) && File.Exists(fileInfo.FilePath))
                        {
                            File.Delete(fileInfo.FilePath);
                        }
                        
                        // Remove from list and refresh
                        _allFiles.Remove(fileInfo);
                        ApplyFileFilter();
                        UpdateStatistics();
                        
                        StatusText.Text = $"تم حذف الملف: {fileInfo.FileName}";
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void AddFileButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة هذه الميزة قريباً", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadProjectFiles();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    // Helper class for file information
    public class ProjectFileInfo
    {
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public DateTime DateAdded { get; set; }
        public string FileType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        
        public string FileSizeFormatted => FileSize > 0 ? $"{FileSize / 1024.0:F1} KB" : "0 KB";
        
        public string FileTypeColor => FileType switch
        {
            "PDF" => "Red",
            "Word" => "Blue",
            "Excel" => "Green",
            "Text" => "Gray",
            _ => "Black"
        };
    }
}
