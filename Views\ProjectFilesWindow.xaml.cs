#nullable enable
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using FinancialTracker.Models;

namespace FinancialTracker.Views
{
    public partial class ProjectFilesWindow : Window
    {
        private readonly int _projectId;
        private List<ProjectFileInfo> _allFiles = new List<ProjectFileInfo>();
        private List<ProjectFileInfo> _filteredFiles = new List<ProjectFileInfo>();

        public ProjectFilesWindow(int projectId)
        {
            InitializeComponent();
            _projectId = projectId;
            Loaded += ProjectFilesWindow_Loaded;
        }

        private async void ProjectFilesWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadProjectFiles();
        }

        private async Task LoadProjectFiles()
        {
            try
            {
                if (StatusText != null)
                    StatusText.Text = "Loading files...";
                
                // Get project info
                var project = await App.DataService.GetProjectByIdAsync(_projectId);
                if (project != null)
                {
                    if (ProjectNameText != null)
                        ProjectNameText.Text = $"Project Files: {project.Name}";
                    Title = $"Project Files - {project.Name}";
                }

                // Get all invoices and commitments for this project
                var invoices = await App.DataService.GetInvoicesAsync();
                var commitments = await App.DataService.GetCommitmentsAsync();

                var projectInvoices = invoices.Where(i => i.ProjectId == _projectId).ToList();
                var projectCommitments = commitments.Where(c => c.ProjectId == _projectId).ToList();

                // Build file list
                _allFiles.Clear();



                // Add invoice files
                foreach (var invoice in projectInvoices.Where(i => !string.IsNullOrEmpty(i.AttachedFileName)))
                {
                    _allFiles.Add(new ProjectFileInfo
                    {
                        FileName = invoice.AttachedFileName ?? "Unknown",
                        FilePath = invoice.AttachedFilePath ?? "",
                        Source = $"Invoice #{invoice.InvoiceNumber}",
                        DateAdded = invoice.CreatedDate,
                        FileType = GetFileType(invoice.AttachedFileName ?? ""),
                        FileSize = GetFileSize(invoice.AttachedFilePath ?? ""),
                        Category = "Invoice"
                    });
                }

                // Add commitment files
                foreach (var commitment in projectCommitments.Where(c => !string.IsNullOrEmpty(c.AttachedFileName)))
                {
                    _allFiles.Add(new ProjectFileInfo
                    {
                        FileName = commitment.AttachedFileName ?? "Unknown",
                        FilePath = commitment.AttachedFilePath ?? "",
                        Source = $"Commitment: {commitment.Title}",
                        DateAdded = commitment.CreatedDate,
                        FileType = GetFileType(commitment.AttachedFileName ?? ""),
                        FileSize = GetFileSize(commitment.AttachedFilePath ?? ""),
                        Category = "Commitment"
                    });
                }

                // Add letter files from invoices
                foreach (var invoice in projectInvoices.Where(i => !string.IsNullOrEmpty(i.LetterFileName)))
                {
                    _allFiles.Add(new ProjectFileInfo
                    {
                        FileName = invoice.LetterFileName ?? "Unknown",
                        FilePath = invoice.LetterFilePath ?? "",
                        Source = $"Letter Invoice #{invoice.InvoiceNumber}",
                        DateAdded = invoice.CreatedDate,
                        FileType = GetFileType(invoice.LetterFileName ?? ""),
                        FileSize = GetFileSize(invoice.LetterFilePath ?? ""),
                        Category = "Letter"
                    });
                }

                // Update UI
                UpdateStatistics();
                _filteredFiles = _allFiles.ToList();
                if (FilesDataGrid != null)
                    FilesDataGrid.ItemsSource = _filteredFiles;

                if (StatusText != null)
                    StatusText.Text = $"Loaded {_allFiles.Count} files";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading files: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                if (StatusText != null)
                    StatusText.Text = "Loading error";
            }
        }

        private void UpdateStatistics()
        {
            if (TotalFilesText != null)
                TotalFilesText.Text = _allFiles.Count.ToString();
            if (CommitmentFilesText != null)
                CommitmentFilesText.Text = _allFiles.Count(f => f.Category == "Commitment").ToString();
            if (InvoiceFilesText != null)
                InvoiceFilesText.Text = _allFiles.Count(f => f.Category == "Invoice").ToString();
            if (LetterFilesText != null)
                LetterFilesText.Text = _allFiles.Count(f => f.Category == "Letter").ToString();

            var totalSize = _allFiles.Sum(f => f.FileSize);
            if (TotalSizeText != null)
                TotalSizeText.Text = $"{totalSize / (1024.0 * 1024.0):F1} MB";
        }

        private string GetFileType(string fileName)
        {
            if (string.IsNullOrEmpty(fileName)) return "Unknown";
            
            var extension = Path.GetExtension(fileName).ToLower();
            return extension switch
            {
                ".pdf" => "PDF",
                ".doc" => "Word",
                ".docx" => "Word",
                ".xls" => "Excel",
                ".xlsx" => "Excel",
                ".txt" => "Text",
                _ => "Other"
            };
        }

        private long GetFileSize(string filePath)
        {
            try
            {
                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    return new FileInfo(filePath).Length;
                }
            }
            catch { }
            return 0;
        }

        private void FileSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFileFilter();
        }

        private void ApplyFileFilter()
        {
            if (_allFiles == null) return;

            var filtered = _allFiles.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(FileSearchTextBox?.Text))
            {
                var searchText = FileSearchTextBox.Text.ToLower();
                filtered = filtered.Where(f => f.FileName.ToLower().Contains(searchText) ||
                                             f.Source.ToLower().Contains(searchText));
            }

            // Apply type filter
            if (FileTypeFilterComboBox?.SelectedItem is ComboBoxItem selectedItem)
            {
                var filterText = selectedItem.Content?.ToString();
                filtered = filterText switch
                {
                    "Invoice Files" => filtered.Where(f => f.Category == "Invoice"),
                    "Commitment Files" => filtered.Where(f => f.Category == "Commitment"),
                    "Letter Files" => filtered.Where(f => f.Category == "Letter"),
                    _ => filtered // "All Files" or default
                };
            }

            _filteredFiles = filtered.ToList();
            if (FilesDataGrid != null)
                FilesDataGrid.ItemsSource = _filteredFiles;
        }

        private void OpenFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectFileInfo fileInfo)
            {
                try
                {
                    if (!string.IsNullOrEmpty(fileInfo.FilePath) && File.Exists(fileInfo.FilePath))
                    {
                        Process.Start(new ProcessStartInfo(fileInfo.FilePath) { UseShellExecute = true });
                        StatusText.Text = $"File opened: {fileInfo.FileName}";
                    }
                    else
                    {
                        MessageBox.Show("File not found or has been deleted", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error opening file: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CopyPathButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectFileInfo fileInfo)
            {
                try
                {
                    if (!string.IsNullOrEmpty(fileInfo.FilePath))
                    {
                        Clipboard.SetText(fileInfo.FilePath);
                        if (StatusText != null)
                            StatusText.Text = "File path copied";
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error copying path: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DeleteFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectFileInfo fileInfo)
            {
                var result = MessageBox.Show($"Are you sure you want to delete the file '{fileInfo.FileName}'?",
                    "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        if (!string.IsNullOrEmpty(fileInfo.FilePath) && File.Exists(fileInfo.FilePath))
                        {
                            File.Delete(fileInfo.FilePath);
                        }
                        
                        // Remove from list and refresh
                        _allFiles.Remove(fileInfo);
                        ApplyFileFilter();
                        UpdateStatistics();
                        
                        if (StatusText != null)
                            StatusText.Text = $"File deleted: {fileInfo.FileName}";
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting file: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void AddLetterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Get invoices for this project
                var invoices = await App.DataService.GetInvoicesByProjectAsync(_projectId);
                var unsignedInvoices = invoices.Where(i => !i.SignatureDate.HasValue).ToList();

                if (!unsignedInvoices.Any())
                {
                    MessageBox.Show("No unsigned invoices found for this project.", "Information",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // Show invoice selection dialog
                var invoiceSelectionDialog = new InvoiceSelectionDialog(unsignedInvoices);
                if (invoiceSelectionDialog.ShowDialog() == true)
                {
                    var selectedInvoice = invoiceSelectionDialog.SelectedInvoice;
                    if (selectedInvoice != null)
                    {
                        // Open file dialog to select letter
                        var openFileDialog = new OpenFileDialog
                        {
                            Title = "Select Letter File",
                            Filter = "PDF Files (*.pdf)|*.pdf|Word Documents (*.doc;*.docx)|*.doc;*.docx|All Files (*.*)|*.*",
                            Multiselect = false
                        };

                        if (openFileDialog.ShowDialog() == true)
                        {
                            var sourceFilePath = openFileDialog.FileName;
                            var fileName = $"Letter Invoice Number {selectedInvoice.InvoiceNumber}.{Path.GetExtension(sourceFilePath).TrimStart('.')}";

                            // Save the file using FileService
                            var savedFilePath = await App.FileService.SaveFileAsync(sourceFilePath, "letters", fileName);

                            // Update invoice with letter information
                            selectedInvoice.LetterFilePath = savedFilePath;
                            selectedInvoice.LetterFileName = fileName;
                            await App.DataService.UpdateInvoiceAsync(selectedInvoice);

                            // Refresh the files list
                            await LoadProjectFiles();
                            if (StatusText != null)
                                StatusText.Text = $"Letter uploaded successfully for Invoice #{selectedInvoice.InvoiceNumber}";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error uploading letter: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddFileButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("This feature will be added soon", "Under Development", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadProjectFiles();
        }

        private void FileTypeFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFileFilter();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    // Helper class for file information
    public class ProjectFileInfo
    {
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public DateTime DateAdded { get; set; }
        public string FileType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string Category { get; set; } = string.Empty;

        public string FileSizeFormatted => FileSize > 0 ? $"{FileSize / 1024.0:F1} KB" : "0 KB";

        public string FileTypeColor => FileType switch
        {
            "PDF" => "#D32F2F",
            "Word" => "#1976D2",
            "Excel" => "#388E3C",
            "Text" => "#757575",
            _ => "#424242"
        };

        public string FileTypeIcon => FileType switch
        {
            "PDF" => "FilePdf",
            "Word" => "FileWord",
            "Excel" => "FileExcel",
            "Text" => "FileDocument",
            _ => "FileDocument"
        };
    }
}
