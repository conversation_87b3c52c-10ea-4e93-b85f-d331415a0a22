﻿#pragma checksum "..\..\..\..\..\Views\ProjectOverviewWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1D8A169BE7D89799119A7B1657373246D7B0C376"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LiveCharts.Wpf;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FinancialTracker {
    
    
    /// <summary>
    /// ProjectOverviewWindow
    /// </summary>
    public partial class ProjectOverviewWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectNameText;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectDescriptionText;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectStatusText;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectCreatedText;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker PODatePicker;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox POAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TasksAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ServicesAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TasksAmountDisplay;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ServicesAmountDisplay;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSpentDisplay;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountDisplay;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar CompletionProgressBar;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompletionPercentageDisplay;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoicesCountDisplay;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoicesTotalDisplay;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CommitmentsCountDisplay;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CommitmentsTotalDisplay;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.PieChart AmountDistributionChart;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart ProgressChart;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Frame InvoicesFrame;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Frame CommitmentsFrame;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FinancialTracker;component/views/projectoverviewwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProjectNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ProjectDescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ProjectStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ProjectCreatedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            
            #line 56 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditProjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.PODatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.POAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.TasksAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.ServicesAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            
            #line 96 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveFinancialDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 98 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TasksAmountDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ServicesAmountDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TotalSpentDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.RemainingAmountDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.CompletionProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 17:
            this.CompletionPercentageDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.InvoicesCountDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.InvoicesTotalDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.CommitmentsCountDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.CommitmentsTotalDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            
            #line 208 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 210 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddCommitmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 212 "..\..\..\..\..\Views\ProjectOverviewWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewReportsButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.AmountDistributionChart = ((LiveCharts.Wpf.PieChart)(target));
            return;
            case 26:
            this.ProgressChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 27:
            this.InvoicesFrame = ((System.Windows.Controls.Frame)(target));
            return;
            case 28:
            this.CommitmentsFrame = ((System.Windows.Controls.Frame)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

