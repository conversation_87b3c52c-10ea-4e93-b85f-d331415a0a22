<Window x:Class="FinancialTracker.ProjectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Project Details" 
        Height="400" 
        Width="500"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Project Information" FontSize="20" FontWeight="Bold" Margin="0,0,0,16"/>

        <!-- Form -->
        <StackPanel Grid.Row="1">
            <TextBox x:Name="NameTextBox"
                     materialDesign:HintAssist.Hint="Project Name"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     MaxLength="200"
                     Margin="0,0,0,16"/>

            <TextBox x:Name="DescriptionTextBox"
                     materialDesign:HintAssist.Hint="Description"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     MaxLength="1000"
                     Height="100"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     VerticalScrollBarVisibility="Auto"
                     Margin="0,0,0,16"/>

            <ComboBox x:Name="StatusComboBox"
                      materialDesign:HintAssist.Hint="Project Status"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,0,16">
                <ComboBoxItem Content="Active"/>
                <ComboBoxItem Content="On Hold"/>
                <ComboBoxItem Content="Completed"/>
                <ComboBoxItem Content="Cancelled"/>
            </ComboBox>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button Content="Cancel" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    Click="CancelButton_Click"/>
            <Button Content="Save" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Padding="16,8"
                    Click="SaveButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
