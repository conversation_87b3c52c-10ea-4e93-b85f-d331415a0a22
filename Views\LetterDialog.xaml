<Window x:Class="FinancialTracker.Views.LetterDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة خطاب" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Background="{DynamicResource MaterialDesignToolBarBackground}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="Email" Width="24" Height="24"
                                       Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Name="txtHeaderTitle" Text="إضافة خطاب جديد" FontSize="20" FontWeight="Bold"
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="16">

                <!-- Basic Information -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Information" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="معلومات الخطاب" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,8,16">
                                <TextBlock Text="عنوان الخطاب *" FontWeight="Medium" Margin="0,0,0,4"/>
                                <TextBox Name="txtTitle" Style="{StaticResource MaterialDesignTextBox}"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="8,0,0,16">
                                <TextBlock Text="تاريخ الإرسال" FontWeight="Medium" Margin="0,0,0,4"/>
                                <DatePicker Name="dpDateSent" Style="{StaticResource MaterialDesignDatePicker}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- File Upload -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="FileUpload" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="ملف الخطاب *" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Button Name="btnSelectFile" Content="اختيار ملف الخطاب"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Click="BtnSelectFile_Click" Margin="0,0,8,0"/>
                            <TextBlock Name="txtSelectedFile" Text="لم يتم اختيار ملف"
                                      VerticalAlignment="Center" FontSize="12" Foreground="Gray"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Certificate Upload (Optional) -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Certificate" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="ملف الشهادة (اختياري)" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Button Name="btnSelectCertificate" Content="اختيار ملف الشهادة"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Click="BtnSelectCertificate_Click" Margin="0,0,8,0"/>
                            <TextBlock Name="txtSelectedCertificate" Text="لم يتم اختيار شهادة"
                                      VerticalAlignment="Center" FontSize="12" Foreground="Gray"/>
                            <Button Name="btnClearCertificate" Content="مسح"
                                    Style="{StaticResource MaterialDesignFlatButton}"
                                    Click="BtnClearCertificate_Click" Margin="8,0,0,0"
                                    Visibility="Collapsed" Foreground="Red"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Commitment Selection -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Link" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="الارتباط المرتبط" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="اختيار الارتباط (اختياري)" FontWeight="Medium" Margin="0,0,0,8"/>
                            <ComboBox Name="cmbCommitment" Style="{StaticResource MaterialDesignComboBox}"
                                     DisplayMemberPath="Title" SelectedValuePath="Id"
                                     materialDesign:HintAssist.Hint="اختر ارتباط..."
                                     SelectionChanged="CmbCommitment_SelectionChanged"/>
                        </StackPanel>

                        <TextBlock Name="txtCommitmentInfo" Text="" FontSize="12"
                                  Foreground="Blue" Margin="0,0,0,8" TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>

                <!-- Invoice Selection -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Receipt" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="الفواتير المرتبطة" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Button Name="btnSelectInvoices" Content="اختيار الفواتير"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Click="BtnSelectInvoices_Click" Margin="0,0,8,0"/>
                            <TextBlock Name="txtSelectedInvoicesCount" Text="لم يتم اختيار فواتير"
                                      VerticalAlignment="Center" FontSize="12" Foreground="Gray"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Selected Items Display -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="FileMultiple" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="الملفات المختارة" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="200">
                            <StackPanel Name="SelectedItemsPanel" Margin="0">
                                <TextBlock Text="لم يتم اختيار ملفات بعد" FontSize="12" Foreground="Gray"
                                          HorizontalAlignment="Center" Margin="20"/>
                            </StackPanel>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Preview Images -->
                <Border Name="PreviewImagesSection" Style="{StaticResource CardStyle}" Visibility="Collapsed">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="ImageMultiple" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="معاينة ملفات الارتباط والفواتير" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- Images Container -->
                        <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled" MaxHeight="300">
                            <StackPanel Name="PreviewImagesContainer" Orientation="Horizontal">
                                <!-- Images will be added dynamically -->
                            </StackPanel>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Notes -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="Note" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="ملاحظات" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <TextBox Name="txtNotes" Style="{StaticResource MaterialDesignTextBox}"
                                Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                materialDesign:HintAssist.Hint="ملاحظات إضافية..."/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="btnSave" Content="حفظ" Style="{StaticResource MaterialDesignRaisedAccentButton}"
                        Width="100" Margin="8,0" Click="BtnSave_Click"/>
                <Button Name="btnCancel" Content="إلغاء" Style="{StaticResource MaterialDesignOutlinedButton}"
                        Width="100" Margin="8,0" Click="BtnCancel_Click"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
