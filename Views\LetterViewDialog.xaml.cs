using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using FinancialTracker.Models;
using FinancialTracker.Services;

namespace FinancialTracker.Views
{
    public partial class LetterViewDialog : Window
    {
        private readonly Letter _letter;

        public LetterViewDialog(Letter letter)
        {
            InitializeComponent();
            _letter = letter;
            LoadLetterData();
        }

        private async void LoadLetterData()
        {
            try
            {
                // Basic information
                lblTitle.Text = _letter.Title;
                lblDateSent.Text = _letter.DateSent?.ToString("yyyy-MM-dd") ?? "لم يتم الإرسال بعد";
                lblNotes.Text = string.IsNullOrEmpty(_letter.Notes) ? "لا توجد ملاحظات" : _letter.Notes;



                // Load commitment info
                await LoadCommitmentInfo();

                // Load invoices info
                await LoadInvoicesInfo();

                // Load certificate info
                LoadCertificateInfo();

                // Load all related files
                await LoadRelatedImages();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الخطاب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private async System.Threading.Tasks.Task LoadCommitmentInfo()
        {
            try
            {
                if (_letter.CommitmentId.HasValue)
                {
                    // Debug: Show what CommitmentId is stored in the letter
                    System.Diagnostics.Debug.WriteLine($"Letter ID: {_letter.Id}, Stored CommitmentId: {_letter.CommitmentId}");

                    var commitment = await App.DataService.GetCommitmentByIdAsync(_letter.CommitmentId.Value);
                    if (commitment != null)
                    {
                        CommitmentSection.Visibility = Visibility.Visible;
                        lblCommitmentTitle.Text = commitment.Title;

                        // Show the actual stored CommitmentId, not the retrieved commitment's ID
                        lblCommitmentNumber.Text = $"#{_letter.CommitmentId.Value}";

                        lblCommitmentDate.Text = commitment.CreatedDate.ToString("yyyy-MM-dd");
                        lblCommitmentAmount.Text = $"{commitment.AmountUSD:F2} USD";

                        // Debug info - يمكن حذف هذا السطر لاحقاً
                        System.Diagnostics.Debug.WriteLine($"Letter CommitmentId: {_letter.CommitmentId}, Retrieved Commitment ID: {commitment.Id}, Title: {commitment.Title}");

                        // Check if there's a mismatch
                        if (_letter.CommitmentId.Value != commitment.Id)
                        {
                            System.Diagnostics.Debug.WriteLine($"WARNING: Mismatch! Letter has CommitmentId {_letter.CommitmentId} but retrieved commitment has ID {commitment.Id}");
                            MessageBox.Show($"تحذير: هناك عدم تطابق في رقم الارتباط!\nالمحفوظ في الخطاب: {_letter.CommitmentId}\nالارتباط المسترجع: {commitment.Id}",
                                "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                    else
                    {
                        CommitmentSection.Visibility = Visibility.Collapsed;
                        System.Diagnostics.Debug.WriteLine($"No commitment found with ID: {_letter.CommitmentId}");
                        MessageBox.Show($"لم يتم العثور على ارتباط برقم: {_letter.CommitmentId}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    CommitmentSection.Visibility = Visibility.Collapsed;
                    System.Diagnostics.Debug.WriteLine("Letter has no CommitmentId");
                }
            }
            catch (Exception ex)
            {
                CommitmentSection.Visibility = Visibility.Collapsed;
                MessageBox.Show($"خطأ في تحميل معلومات الارتباط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async System.Threading.Tasks.Task LoadInvoicesInfo()
        {
            try
            {
                InvoicesContainer.Children.Clear();

                if (!string.IsNullOrEmpty(_letter.SelectedInvoiceIds))
                {
                    var invoiceIds = _letter.SelectedInvoiceIds.Split(',')
                        .Where(id => int.TryParse(id.Trim(), out _))
                        .Select(id => int.Parse(id.Trim()))
                        .ToList();

                    if (invoiceIds.Any())
                    {
                        var invoices = await App.DataService.GetInvoicesByProjectAsync(_letter.ProjectId);
                        var selectedInvoices = invoices.Where(i => invoiceIds.Contains(i.Id)).ToList();

                        if (selectedInvoices.Any())
                        {
                            InvoicesSection.Visibility = Visibility.Visible;

                            foreach (var invoice in selectedInvoices)
                            {
                                AddInvoiceCard(invoice);
                            }
                        }
                        else
                        {
                            InvoicesSection.Visibility = Visibility.Collapsed;
                        }
                    }
                    else
                    {
                        InvoicesSection.Visibility = Visibility.Collapsed;
                    }
                }
                else
                {
                    InvoicesSection.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                InvoicesSection.Visibility = Visibility.Collapsed;
                MessageBox.Show($"خطأ في تحميل معلومات الفواتير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void AddInvoiceCard(Invoice invoice)
        {
            var border = new Border
            {
                BorderBrush = Brushes.DarkBlue,
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 5, 0, 5),
                Padding = new Thickness(15),
                Background = new SolidColorBrush(Color.FromRgb(240, 248, 255))
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Invoice Number
            var numberPanel = new StackPanel { Margin = new Thickness(0, 0, 8, 0) };
            var numberLabel = new TextBlock
            {
                Text = "رقم الفاتورة:",
                FontWeight = FontWeights.Bold,
                FontSize = 12,
                Margin = new Thickness(0, 0, 0, 4)
            };
            var numberValue = new TextBlock
            {
                Text = invoice.InvoiceNumber,
                FontSize = 14,
                Background = Brushes.LightYellow,
                Padding = new Thickness(6),
                TextAlignment = TextAlignment.Center
            };
            numberPanel.Children.Add(numberLabel);
            numberPanel.Children.Add(numberValue);
            Grid.SetColumn(numberPanel, 0);
            grid.Children.Add(numberPanel);

            // Invoice Date
            var datePanel = new StackPanel { Margin = new Thickness(4, 0, 4, 0) };
            var dateLabel = new TextBlock
            {
                Text = "تاريخ الفاتورة:",
                FontWeight = FontWeights.Bold,
                FontSize = 12,
                Margin = new Thickness(0, 0, 0, 4)
            };
            var dateValue = new TextBlock
            {
                Text = invoice.InvoiceDate.ToString("yyyy-MM-dd"),
                FontSize = 14,
                Background = Brushes.LightCyan,
                Padding = new Thickness(6),
                TextAlignment = TextAlignment.Center
            };
            datePanel.Children.Add(dateLabel);
            datePanel.Children.Add(dateValue);
            Grid.SetColumn(datePanel, 1);
            grid.Children.Add(datePanel);

            // Invoice Amount
            var amountPanel = new StackPanel { Margin = new Thickness(4, 0, 8, 0) };
            var amountLabel = new TextBlock
            {
                Text = "قيمة الفاتورة:",
                FontWeight = FontWeights.Bold,
                FontSize = 12,
                Margin = new Thickness(0, 0, 0, 4)
            };
            var amountValue = new TextBlock
            {
                Text = $"{invoice.AmountUSD:F2} USD",
                FontSize = 14,
                Background = Brushes.LightGreen,
                Padding = new Thickness(6),
                TextAlignment = TextAlignment.Center,
                FontWeight = FontWeights.Bold
            };
            amountPanel.Children.Add(amountLabel);
            amountPanel.Children.Add(amountValue);
            Grid.SetColumn(amountPanel, 2);
            grid.Children.Add(amountPanel);

            border.Child = grid;
            InvoicesContainer.Children.Add(border);
        }

        private void LoadCertificateInfo()
        {
            try
            {
                if (!string.IsNullOrEmpty(_letter.CertificateFileName))
                {
                    CertificateSection.Visibility = Visibility.Visible;
                    lblCertificateFileName.Text = _letter.CertificateFileName;

                    // Check if file exists
                    if (!string.IsNullOrEmpty(_letter.CertificateFilePath) && File.Exists(_letter.CertificateFilePath))
                    {
                        btnOpenCertificate.IsEnabled = true;
                    }
                    else
                    {
                        btnOpenCertificate.IsEnabled = false;
                        lblCertificateFileName.Text += " (الملف غير موجود)";
                    }
                }
                else
                {
                    CertificateSection.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                CertificateSection.Visibility = Visibility.Collapsed;
                System.Diagnostics.Debug.WriteLine($"Error loading certificate info: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task LoadRelatedImages()
        {
            try
            {
                FilesContainer.Children.Clear();
                NoFilesMessage.Visibility = Visibility.Visible;
                var hasFiles = false;

                // 1. Letter file - any file type
                if (!string.IsNullOrEmpty(_letter.LetterFilePath) && File.Exists(_letter.LetterFilePath))
                {
                    AddFileControl("📄 ملف الخطاب المرفوع",
                        _letter.LetterFileName ?? Path.GetFileName(_letter.LetterFilePath),
                        _letter.LetterFilePath);
                    hasFiles = true;
                }

                // 2. Load commitment file if selected
                if (_letter.CommitmentId.HasValue)
                {
                    var commitment = await App.DataService.GetCommitmentByIdAsync(_letter.CommitmentId.Value);
                    if (commitment != null && !string.IsNullOrEmpty(commitment.AttachedFileName))
                    {
                        var commitmentFilePath = Path.Combine("Files", "Projects", _letter.ProjectId.ToString(), "Commitments", commitment.AttachedFileName);
                        if (File.Exists(commitmentFilePath))
                        {
                            AddFileControl($"🔗 ملف الارتباط: {commitment.Title}",
                                commitment.AttachedFileName,
                                commitmentFilePath,
                                $"القيمة: {commitment.AmountUSD:C}");
                            hasFiles = true;
                        }
                    }
                }

                // 3. Load selected invoice files
                if (!string.IsNullOrEmpty(_letter.SelectedInvoiceIds))
                {
                    var invoiceIds = _letter.SelectedInvoiceIds.Split(',')
                        .Where(id => int.TryParse(id.Trim(), out _))
                        .Select(id => int.Parse(id.Trim()))
                        .ToList();

                    var invoices = await App.DataService.GetInvoicesByProjectAsync(_letter.ProjectId);

                    foreach (var invoiceId in invoiceIds)
                    {
                        var invoice = invoices.FirstOrDefault(i => i.Id == invoiceId);
                        if (invoice != null && !string.IsNullOrEmpty(invoice.AttachedFileName))
                        {
                            var invoiceFilePath = Path.Combine("Files", "Projects", _letter.ProjectId.ToString(), "Invoices", invoice.AttachedFileName);
                            if (File.Exists(invoiceFilePath))
                            {
                                AddFileControl($"📋 ملف فاتورة {invoice.InvoiceNumber}",
                                    invoice.AttachedFileName,
                                    invoiceFilePath,
                                    $"القيمة: {invoice.AmountUSD:C}");
                                hasFiles = true;
                            }
                        }
                    }
                }

                if (hasFiles)
                {
                    NoFilesMessage.Visibility = Visibility.Collapsed;
                }


            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملفات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddFileControl(string title, string fileName, string filePath, string? additionalInfo = null)
        {
            var border = new Border
            {
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 5, 0, 5),
                Padding = new Thickness(15),
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250))
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // File info panel
            var infoPanel = new StackPanel();

            // Title
            var titleText = new TextBlock
            {
                Text = title,
                FontWeight = FontWeights.Bold,
                FontSize = 14,
                Margin = new Thickness(0, 0, 0, 5)
            };
            infoPanel.Children.Add(titleText);

            // File name
            var fileNameText = new TextBlock
            {
                Text = $"📁 {fileName}",
                FontSize = 12,
                Foreground = Brushes.DarkBlue,
                Margin = new Thickness(0, 0, 0, 3)
            };
            infoPanel.Children.Add(fileNameText);

            // Additional info (like value)
            if (!string.IsNullOrEmpty(additionalInfo))
            {
                var additionalText = new TextBlock
                {
                    Text = additionalInfo,
                    FontSize = 11,
                    Foreground = Brushes.DarkGreen,
                    Margin = new Thickness(0, 0, 0, 3)
                };
                infoPanel.Children.Add(additionalText);
            }

            // File status
            var statusText = new TextBlock
            {
                Text = File.Exists(filePath) ? "✅ الملف متاح" : "❌ الملف غير موجود",
                FontSize = 10,
                Foreground = File.Exists(filePath) ? Brushes.Green : Brushes.Red
            };
            infoPanel.Children.Add(statusText);

            Grid.SetColumn(infoPanel, 0);
            grid.Children.Add(infoPanel);

            // Open button
            var openButton = new Button
            {
                Content = "فتح الملف",
                Width = 80,
                Height = 35,
                Margin = new Thickness(10, 0, 0, 0),
                FontSize = 11,
                IsEnabled = File.Exists(filePath)
            };

            openButton.Click += (s, e) => OpenFile(filePath);
            Grid.SetColumn(openButton, 1);
            grid.Children.Add(openButton);

            border.Child = grid;
            FilesContainer.Children.Add(border);
        }

        private void OpenFile(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editDialog = new LetterDialog(App.DataService, App.FileService, _letter.ProjectId, _letter);
                if (editDialog.ShowDialog() == true)
                {
                    // Refresh the display
                    LoadLetterData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة التعديل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnOpenCertificate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(_letter.CertificateFilePath) && File.Exists(_letter.CertificateFilePath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = _letter.CertificateFilePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("ملف الشهادة غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح ملف الشهادة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
