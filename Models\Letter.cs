using System;
using System.ComponentModel.DataAnnotations;

namespace FinancialTracker.Models
{
    public class Letter
    {
        public int Id { get; set; }

        [Required]
        public string Title { get; set; } = string.Empty;

        // Foreign Keys
        public int ProjectId { get; set; }
        public Project? Project { get; set; }

        public int? CommitmentId { get; set; }
        public Commitment? Commitment { get; set; }

        // Selected invoices (comma-separated IDs)
        public string? SelectedInvoiceIds { get; set; }

        // Letter file path
        public string? LetterFilePath { get; set; }
        public string? LetterFileName { get; set; }

        // Certificate file path (optional)
        public string? CertificateFilePath { get; set; }
        public string? CertificateFileName { get; set; }

        // Dates
        public DateTime DateCreated { get; set; } = DateTime.Now;
        public DateTime? DateSent { get; set; }

        // Status and notes
        public string Status { get; set; } = "Draft"; // Draft, Sent, Delivered
        public string? Notes { get; set; }

        // Tracking
        public string CreatedBy { get; set; } = "System";
        public DateTime LastModified { get; set; } = DateTime.Now;
    }
}
