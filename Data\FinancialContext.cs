using System;
using System.Collections.Generic;
using System.IO;
using Microsoft.EntityFrameworkCore;
using FinancialTracker.Models;

namespace FinancialTracker.Data
{
    public class FinancialContext : DbContext
    {
        public FinancialContext() { }

        public FinancialContext(DbContextOptions<FinancialContext> options) : base(options) { }

        public DbSet<Project> Projects { get; set; } = null!;
        public DbSet<Invoice> Invoices { get; set; } = null!;
        public DbSet<Commitment> Commitments { get; set; } = null!;
        public DbSet<Reply> Replies { get; set; } = null!;
        public DbSet<Site> Sites { get; set; } = null!;
        public DbSet<ProjectSite> ProjectSites { get; set; }
        public DbSet<Letter> Letters { get; set; } = null!;

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "FinancialTracker.db");
                optionsBuilder.UseSqlite($"Data Source={dbPath}");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships
            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.Project)
                .WithMany(p => p.Invoices)
                .HasForeignKey(i => i.ProjectId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.Commitment)
                .WithMany(c => c.Invoices)
                .HasForeignKey(i => i.CommitmentId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.Site)
                .WithMany()
                .HasForeignKey(i => i.SiteId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Commitment>()
                .HasOne(c => c.Project)
                .WithMany(p => p.Commitments)
                .HasForeignKey(c => c.ProjectId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Commitment>()
                .HasOne(c => c.Site)
                .WithMany()
                .HasForeignKey(c => c.SiteId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ProjectSite>()
                .HasOne(ps => ps.Project)
                .WithMany(p => p.ProjectSites)
                .HasForeignKey(ps => ps.ProjectId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ProjectSite>()
                .HasOne(ps => ps.Site)
                .WithMany(s => s.ProjectSites)
                .HasForeignKey(ps => ps.SiteId)
                .OnDelete(DeleteBehavior.Restrict);

            // Ensure unique combination of Project and Site
            modelBuilder.Entity<ProjectSite>()
                .HasIndex(ps => new { ps.ProjectId, ps.SiteId })
                .IsUnique();

            modelBuilder.Entity<Reply>()
                .HasOne(r => r.Invoice)
                .WithMany(i => i.Replies)
                .HasForeignKey(r => r.InvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Reply>()
                .HasOne(r => r.Commitment)
                .WithMany(c => c.Replies)
                .HasForeignKey(r => r.CommitmentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure default values
            modelBuilder.Entity<Invoice>()
                .Property(i => i.PaidAmount)
                .HasDefaultValue(0m);

            modelBuilder.Entity<Invoice>()
                .Property(i => i.Type)
                .HasDefaultValue("Task");

            modelBuilder.Entity<Project>()
                .Property(p => p.Status)
                .HasDefaultValue("Active");

            modelBuilder.Entity<Site>()
                .Property(s => s.Status)
                .HasDefaultValue("Active");

            modelBuilder.Entity<ProjectSite>()
                .Property(ps => ps.Status)
                .HasDefaultValue("Active");

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed predefined projects - المشاريع التي كانت موجودة من قبل
            var baseDate = new DateTime(2024, 1, 1);

            // Seed Sites
            modelBuilder.Entity<Site>().HasData(
                new Site
                {
                    Id = 1,
                    Name = "nn1",
                    DisplayName = "الموقع الأول - NN1",
                    Description = "الموقع الرئيسي الأول",
                    Status = "Active",
                    CreatedDate = baseDate
                },
                new Site
                {
                    Id = 2,
                    Name = "nn2",
                    DisplayName = "الموقع الثاني - NN2",
                    Description = "الموقع الرئيسي الثاني",
                    Status = "Active",
                    CreatedDate = baseDate
                }
            );

            modelBuilder.Entity<Project>().HasData(
                new Project { Id = 1, Name = "UMS", Description = "University Management System", Status = "Active", CreatedDate = baseDate },
                new Project { Id = 2, Name = "BNG", Description = "Banking and Finance System", Status = "Active", CreatedDate = baseDate.AddDays(5) },
                new Project { Id = 3, Name = "AAA", Description = "Authentication and Authorization", Status = "Active", CreatedDate = baseDate.AddDays(10) },
                new Project { Id = 4, Name = "NTP", Description = "Network Time Protocol", Status = "Active", CreatedDate = baseDate.AddDays(15) },
                new Project { Id = 5, Name = "HPBX", Description = "High Performance Business Exchange", Status = "Active", CreatedDate = baseDate.AddDays(20) },
                new Project { Id = 6, Name = "ERP System", Description = "Enterprise Resource Planning System", Status = "Active", CreatedDate = baseDate.AddDays(25) },
                new Project { Id = 7, Name = "CRM Platform", Description = "Customer Relationship Management Platform", Status = "Active", CreatedDate = baseDate.AddDays(30) },
                new Project { Id = 8, Name = "E-Commerce", Description = "E-Commerce Website and Mobile App", Status = "Active", CreatedDate = baseDate.AddDays(35) },
                new Project { Id = 9, Name = "Data Analytics", Description = "Business Intelligence and Data Analytics Platform", Status = "Active", CreatedDate = baseDate.AddDays(40) },
                new Project { Id = 10, Name = "Mobile App", Description = "Cross-Platform Mobile Application", Status = "Active", CreatedDate = baseDate.AddDays(45) }
            );

            // Seed ProjectSites - ربط كل مشروع بالموقعين
            var projectSites = new List<object>();
            for (int projectId = 1; projectId <= 10; projectId++)
            {
                // ربط كل مشروع بالموقع الأول
                projectSites.Add(new
                {
                    Id = (projectId - 1) * 2 + 1,
                    ProjectId = projectId,
                    SiteId = 1,
                    AssignedDate = baseDate.AddDays(projectId * 5),
                    Status = "Active"
                });

                // ربط كل مشروع بالموقع الثاني
                projectSites.Add(new
                {
                    Id = (projectId - 1) * 2 + 2,
                    ProjectId = projectId,
                    SiteId = 2,
                    AssignedDate = baseDate.AddDays(projectId * 5),
                    Status = "Active"
                });
            }

            modelBuilder.Entity<ProjectSite>().HasData(projectSites.ToArray());
        }
    }
}
