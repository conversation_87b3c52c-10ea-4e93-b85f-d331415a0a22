<Window x:Class="FinancialTracker.Views.InvoiceChoiceDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Add Invoice to Commitment" Height="500" Width="700"
        WindowStartupLocation="CenterOwner"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,16">
            <TextBlock Text="إضافة فاتورة للارتباط" FontSize="18" FontWeight="Bold" Margin="0,0,0,8"/>
            <TextBlock Text="يمكنك اختيار فاتورة موجودة في المشروع (غير مرتبطة) أو إنشاء فاتورة جديدة" 
                       FontSize="12" Opacity="0.7"/>
        </StackPanel>

        <!-- Content -->
        <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
            
            <!-- Existing Invoices Tab -->
            <TabItem Header="الفواتير المتاحة">
                <Grid Margin="0,16,0,0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="اختر فاتورة موجودة في المشروع (غير مرتبطة بأي ارتباط):" 
                               FontWeight="Medium" Margin="0,0,0,8"/>
                    
                    <DataGrid Grid.Row="1" x:Name="ExistingInvoicesDataGrid" 
                              AutoGenerateColumns="False" CanUserAddRows="False"
                              IsReadOnly="True" SelectionMode="Single"
                              GridLinesVisibility="Horizontal">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="*"/>
                            <DataGridTextColumn Header="المبلغ" Binding="{Binding AmountUSD, StringFormat='{}{0:C0}'}" Width="*"/>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="2*"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding InvoiceDate, StringFormat='{}{0:yyyy-MM-dd}'}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <!-- Create New Tab -->
            <TabItem Header="Create New Invoice">
                <Grid Margin="0,16,0,0">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="Plus" Width="64" Height="64" Opacity="0.5" HorizontalAlignment="Center"/>
                        <TextBlock Text="Create New Invoice" FontSize="16" FontWeight="Medium"
                                   HorizontalAlignment="Center" Margin="0,8,0,4"/>
                        <TextBlock Text="A new invoice creation window will open and link it to this commitment" FontSize="12" Opacity="0.7"
                                   HorizontalAlignment="Center" TextWrapping="Wrap" TextAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button x:Name="SelectButton" Content="اختيار" 
                    Style="{StaticResource MaterialDesignRaisedButton}" 
                    Margin="0,0,8,0" Click="SelectButton_Click"/>
            <Button x:Name="CancelButton" Content="إلغاء" 
                    Style="{StaticResource MaterialDesignOutlinedButton}" 
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
