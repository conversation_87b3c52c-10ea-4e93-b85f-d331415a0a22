using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;

namespace FinancialTracker.Views
{
    public partial class InvoiceChoiceDialog : Window
    {
        private int _projectId;
        private int _commitmentId;
        private List<Invoice> _availableInvoices = new List<Invoice>();

        public InvoiceChoiceDialog(int projectId, int commitmentId)
        {
            InitializeComponent();
            _projectId = projectId;
            _commitmentId = commitmentId;
            LoadAvailableInvoices();
        }

        private async void LoadAvailableInvoices()
        {
            try
            {
                var allInvoices = await App.DataService.GetInvoicesAsync();
                _availableInvoices = allInvoices.Where(i => 
                    i.ProjectId == _projectId && 
                    i.CommitmentId == null).ToList();
                
                ExistingInvoicesDataGrid.ItemsSource = _availableInvoices;
                
                // Update UI based on available invoices
                if (_availableInvoices.Count == 0)
                {
                    // No available invoices, show message
                    var noInvoicesMessage = new TextBlock
                    {
                        Text = "No available invoices in this project (all invoices are already linked)",
                        FontSize = 14,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        Opacity = 0.7,
                        TextWrapping = TextWrapping.Wrap,
                        TextAlignment = TextAlignment.Center
                    };
                    
                    var grid = ExistingInvoicesDataGrid.Parent as Grid;
                    if (grid != null)
                    {
                        grid.Children.Remove(ExistingInvoicesDataGrid);
                        grid.Children.Add(noInvoicesMessage);
                        Grid.SetRow(noInvoicesMessage, 1);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading invoices: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            // First, check if the commitment has any amount
            try
            {
                var commitment = await App.DataService.GetCommitmentByIdAsync(_commitmentId);
                if (commitment == null)
                {
                    MessageBox.Show("لم يتم العثور على الارتباط.", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (commitment.AmountUSD <= 0)
                {
                    MessageBox.Show("لا يمكن إضافة فاتورة لهذا الارتباط لأنه لا يحتوي على مبلغ مالي.", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من الارتباط: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // Find TabControl
            var tabControl = FindVisualChild<TabControl>(this);

            if (tabControl != null && tabControl.SelectedIndex == 0)
            {
                // Existing invoices tab is selected
                if (ExistingInvoicesDataGrid.SelectedItem is Invoice selectedInvoice)
                {
                    try
                    {
                        // Link the selected invoice to this commitment
                        selectedInvoice.CommitmentId = _commitmentId;
                        await App.DataService.UpdateInvoiceAsync(selectedInvoice);
                        
                        MessageBox.Show("تم ربط الفاتورة بالارتباط بنجاح!", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        
                        DialogResult = true;
                        Close();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error linking invoice: {ex.Message}", "Error", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار فاتورة من القائمة.", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            else
            {
                // Create new invoice tab is selected
                try
                {
                    var dialog = new InvoiceDialog(null, _projectId, _commitmentId);
                    if (dialog.ShowDialog() == true)
                    {
                        DialogResult = true;
                        Close();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error creating invoice: {ex.Message}", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // Helper method to find child controls
        private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = System.Windows.Media.VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;
                
                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }
    }
}
