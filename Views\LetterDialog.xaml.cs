using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using FinancialTracker.Models;
using FinancialTracker.Services;

namespace FinancialTracker.Views
{
    public partial class LetterDialog : Window
    {
        private readonly IDataService _dataService;
        private readonly IFileService _fileService;
        private Letter? _letter;
        private readonly int _projectId;
        private string? _selectedFilePath;
        private string? _selectedCertificatePath;
        private List<Commitment> _commitments = new();
        private List<Invoice> _invoices = new();
        private List<int> _selectedInvoiceIds = new();

        public Letter? Letter => _letter;

        public LetterDialog(IDataService dataService, IFileService fileService, int projectId, Letter? letter = null)
        {
            InitializeComponent();
            _dataService = dataService;
            _fileService = fileService;
            _projectId = projectId;
            _letter = letter;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // Load commitments for the project
                _commitments = await _dataService.GetCommitmentsByProjectAsync(_projectId);
                cmbCommitment.ItemsSource = _commitments;

                // Load invoices for the project
                _invoices = await _dataService.GetInvoicesByProjectAsync(_projectId);

                if (_letter != null)
                {
                    txtHeaderTitle.Text = "تعديل الخطاب";

                    // Load letter data
                    txtTitle.Text = _letter.Title;
                    txtNotes.Text = _letter.Notes ?? "";

                    // Set date sent if exists
                    if (_letter.DateSent.HasValue)
                    {
                        dpDateSent.SelectedDate = _letter.DateSent.Value;
                    }

                    // Set selected commitment
                    if (_letter.CommitmentId.HasValue)
                    {
                        cmbCommitment.SelectedValue = _letter.CommitmentId.Value;
                    }

                    // Show file info if exists
                    if (!string.IsNullOrEmpty(_letter.LetterFileName))
                    {
                        txtSelectedFile.Text = _letter.LetterFileName;
                        txtSelectedFile.Foreground = System.Windows.Media.Brushes.Green;
                    }

                    // Show certificate info if exists
                    if (!string.IsNullOrEmpty(_letter.CertificateFileName))
                    {
                        txtSelectedCertificate.Text = _letter.CertificateFileName;
                        txtSelectedCertificate.Foreground = System.Windows.Media.Brushes.Green;
                        btnClearCertificate.Visibility = Visibility.Visible;
                    }

                    // Load selected invoices
                    if (!string.IsNullOrEmpty(_letter.SelectedInvoiceIds))
                    {
                        var invoiceIds = _letter.SelectedInvoiceIds.Split(',')
                            .Where(id => int.TryParse(id.Trim(), out _))
                            .Select(id => int.Parse(id.Trim()))
                            .ToList();
                        _selectedInvoiceIds = invoiceIds;
                        UpdateSelectedItemsDisplay();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSelectInvoices_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new MultiSelectDialog("اختيار الفواتير",
                    _invoices.Cast<object>(),
                    invoice => $"فاتورة {((Invoice)invoice).InvoiceNumber} - {((Invoice)invoice).AmountUSD:C}",
                    _invoices.Where(i => _selectedInvoiceIds.Contains(i.Id)).Cast<object>());

                if (dialog.ShowDialog() == true)
                {
                    _selectedInvoiceIds = dialog.SelectedItems.Cast<Invoice>().Select(i => i.Id).ToList();
                    UpdateSelectedItemsDisplay();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار الفواتير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateSelectedItemsDisplay()
        {
            SelectedItemsPanel.Children.Clear();

            // Add header
            var headerText = new TextBlock
            {
                Text = "الملفات المختارة:",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            SelectedItemsPanel.Children.Add(headerText);

            var hasItems = false;

            // Add commitment if selected
            if (cmbCommitment.SelectedItem is Commitment selectedCommitment)
            {
                AddSelectedItemControl(selectedCommitment.Title,
                    selectedCommitment.AmountUSD.ToString("C"),
                    selectedCommitment.AttachedFileName,
                    "commitment");
                hasItems = true;
            }

            // Add selected invoices
            foreach (var invoiceId in _selectedInvoiceIds)
            {
                var invoice = _invoices.FirstOrDefault(i => i.Id == invoiceId);
                if (invoice != null)
                {
                    AddSelectedItemControl($"فاتورة {invoice.InvoiceNumber}",
                        invoice.AmountUSD.ToString("C"),
                        invoice.AttachedFileName,
                        "invoice");
                    hasItems = true;
                }
            }

            // Update invoice count display
            txtSelectedInvoicesCount.Text = _selectedInvoiceIds.Count > 0
                ? $"تم اختيار {_selectedInvoiceIds.Count} فاتورة"
                : "لم يتم اختيار فواتير";

            // Show message if no items
            if (!hasItems)
            {
                var noItemsText = new TextBlock
                {
                    Text = "لم يتم اختيار ملفات بعد",
                    FontSize = 12,
                    Foreground = Brushes.Gray,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(20)
                };
                SelectedItemsPanel.Children.Add(noItemsText);
            }
        }

        private void AddSelectedItemControl(string title, string value, string? fileName, string type)
        {
            var border = new Border
            {
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 5, 0, 5),
                Padding = new Thickness(10),
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250))
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // Title and value
            var infoPanel = new StackPanel();

            var titleText = new TextBlock
            {
                Text = title,
                FontWeight = FontWeights.Bold,
                FontSize = 12
            };
            infoPanel.Children.Add(titleText);

            var valueText = new TextBlock
            {
                Text = $"القيمة: {value}",
                FontSize = 11,
                Foreground = Brushes.DarkBlue,
                Margin = new Thickness(0, 2, 0, 0)
            };
            infoPanel.Children.Add(valueText);

            if (!string.IsNullOrEmpty(fileName))
            {
                var fileText = new TextBlock
                {
                    Text = $"الملف: {fileName}",
                    FontSize = 10,
                    Foreground = Brushes.Gray,
                    Margin = new Thickness(0, 2, 0, 0)
                };
                infoPanel.Children.Add(fileText);
            }

            Grid.SetColumn(infoPanel, 0);
            grid.Children.Add(infoPanel);

            // Open button
            if (!string.IsNullOrEmpty(fileName))
            {
                var openButton = new Button
                {
                    Content = "فتح",
                    Width = 50,
                    Height = 25,
                    Margin = new Thickness(5, 0, 0, 0),
                    FontSize = 10
                };

                openButton.Click += (s, e) => OpenFile(fileName, type);
                Grid.SetColumn(openButton, 1);
                grid.Children.Add(openButton);
            }

            border.Child = grid;
            SelectedItemsPanel.Children.Add(border);
        }

        private void OpenFile(string fileName, string type)
        {
            try
            {
                string filePath = "";

                if (type == "commitment")
                {
                    filePath = Path.Combine("Files", "Projects", _projectId.ToString(), "Commitments", fileName);
                }
                else if (type == "invoice")
                {
                    filePath = Path.Combine("Files", "Projects", _projectId.ToString(), "Invoices", fileName);
                }

                if (File.Exists(filePath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSelectFile_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار ملف الخطاب",
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات Word (*.docx)|*.docx|الصور (*.jpg;*.png)|*.jpg;*.png",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                _selectedFilePath = openFileDialog.FileName;
                txtSelectedFile.Text = Path.GetFileName(_selectedFilePath);
                txtSelectedFile.Foreground = System.Windows.Media.Brushes.Green;
            }
        }

        private void BtnSelectCertificate_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار ملف الشهادة",
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات Word (*.docx)|*.docx|الصور (*.jpg;*.png)|*.jpg;*.png",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                _selectedCertificatePath = openFileDialog.FileName;
                txtSelectedCertificate.Text = Path.GetFileName(_selectedCertificatePath);
                txtSelectedCertificate.Foreground = System.Windows.Media.Brushes.Green;
                btnClearCertificate.Visibility = Visibility.Visible;
            }
        }

        private void BtnClearCertificate_Click(object sender, RoutedEventArgs e)
        {
            _selectedCertificatePath = null;
            txtSelectedCertificate.Text = "لم يتم اختيار شهادة";
            txtSelectedCertificate.Foreground = System.Windows.Media.Brushes.Gray;
            btnClearCertificate.Visibility = Visibility.Collapsed;
        }

        private void SetComboBoxValue(ComboBox comboBox, string value)
        {
            foreach (ComboBoxItem item in comboBox.Items)
            {
                if (item.Tag?.ToString() == value)
                {
                    comboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private async void CmbCommitment_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cmbCommitment.SelectedValue != null)
                {
                    var commitmentId = (int)cmbCommitment.SelectedValue;
                    await LoadCommitmentPreviewImages(commitmentId);

                    // Show commitment info
                    var selectedCommitment = _commitments.FirstOrDefault(c => c.Id == commitmentId);
                    if (selectedCommitment != null)
                    {
                        txtCommitmentInfo.Text = $"الارتباط: {selectedCommitment.Title} - المبلغ: {selectedCommitment.AmountUSD.ToString("C")}";
                    }

                    // Update selected items display
                    UpdateSelectedItemsDisplay();
                }
                else
                {
                    PreviewImagesSection.Visibility = Visibility.Collapsed;
                    PreviewImagesContainer.Children.Clear();
                    txtCommitmentInfo.Text = "";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معاينة الصور: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadCommitmentPreviewImages(int commitmentId)
        {
            try
            {
                PreviewImagesContainer.Children.Clear();
                var allFiles = new List<ImageFileInfo>();

                // Get commitment details - any file type
                var commitment = await App.DataService.GetCommitmentByIdAsync(commitmentId);
                if (commitment != null && !string.IsNullOrEmpty(commitment.AttachedFileName))
                {
                    var commitmentFilePath = Path.Combine("Files", "Projects", _projectId.ToString(), "Commitments", commitment.AttachedFileName);
                    if (File.Exists(commitmentFilePath))
                    {
                        allFiles.Add(new ImageFileInfo
                        {
                            Title = "2️⃣ ملف الارتباط",
                            FilePath = commitmentFilePath,
                            FileName = commitment.AttachedFileName
                        });
                    }
                }

                // Get related invoices - any file type
                var invoices = await App.DataService.GetInvoicesByProjectAsync(_projectId);
                var commitmentInvoices = invoices.Where(i => i.CommitmentId == commitmentId).ToList();

                foreach (var invoice in commitmentInvoices)
                {
                    if (!string.IsNullOrEmpty(invoice.AttachedFileName))
                    {
                        var invoiceFilePath = Path.Combine("Files", "Projects", _projectId.ToString(), "Invoices", invoice.AttachedFileName);
                        if (File.Exists(invoiceFilePath))
                        {
                            allFiles.Add(new ImageFileInfo
                            {
                                Title = $"3️⃣ ملف فاتورة {invoice.InvoiceNumber}",
                                FilePath = invoiceFilePath,
                                FileName = invoice.AttachedFileName
                            });
                        }
                    }
                }

                // Create file controls
                foreach (var file in allFiles)
                {
                    CreatePreviewFileControl(file);
                }

                // Add summary if there are files
                if (allFiles.Any())
                {
                    var summaryText = new TextBlock
                    {
                        Text = $"عدد الملفات المرتبطة: {allFiles.Count}",
                        FontSize = 11,
                        Foreground = Brushes.DarkBlue,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(10),
                        FontWeight = FontWeights.Bold
                    };
                    PreviewImagesContainer.Children.Insert(0, summaryText);
                    PreviewImagesSection.Visibility = Visibility.Visible;
                }
                else
                {
                    PreviewImagesContainer.Children.Clear();
                    var noFilesPanel = new StackPanel
                    {
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(20)
                    };

                    var iconText = new TextBlock
                    {
                        Text = "📁",
                        FontSize = 32,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Foreground = Brushes.LightGray,
                        Margin = new Thickness(0, 0, 0, 5)
                    };

                    var messageText = new TextBlock
                    {
                        Text = "لا توجد ملفات مرتبطة بهذا الارتباط\n(لا توجد ملفات للارتباط أو الفواتير)",
                        FontSize = 12,
                        Foreground = Brushes.Gray,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        TextWrapping = TextWrapping.Wrap
                    };

                    noFilesPanel.Children.Add(iconText);
                    noFilesPanel.Children.Add(messageText);
                    PreviewImagesContainer.Children.Add(noFilesPanel);
                    PreviewImagesSection.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صور المعاينة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreatePreviewFileControl(ImageFileInfo fileInfo)
        {
            try
            {
                var border = new Border
                {
                    Width = 200,
                    Height = 250,
                    Margin = new Thickness(5),
                    BorderBrush = Brushes.LightGray,
                    BorderThickness = new Thickness(1),
                    CornerRadius = new CornerRadius(8)
                };

                var stackPanel = new StackPanel();

                // Title
                var titleText = new TextBlock
                {
                    Text = fileInfo.Title,
                    FontWeight = FontWeights.Bold,
                    FontSize = 10,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(3),
                    Background = Brushes.LightBlue,
                    Padding = new Thickness(3)
                };
                stackPanel.Children.Add(titleText);

                // File preview (image or icon)
                if (IsImageFile(fileInfo.FilePath))
                {
                    // Show actual image
                    var image = new Image
                    {
                        Width = 180,
                        Height = 150,
                        Stretch = Stretch.Uniform,
                        Margin = new Thickness(3)
                    };

                    try
                    {
                        var bitmap = new BitmapImage();
                        bitmap.BeginInit();
                        bitmap.UriSource = new Uri(fileInfo.FilePath, UriKind.Absolute);
                        bitmap.DecodePixelWidth = 180;
                        bitmap.EndInit();
                        image.Source = bitmap;
                        stackPanel.Children.Add(image);
                    }
                    catch
                    {
                        // If image loading fails, show file icon
                        stackPanel.Children.Add(CreatePreviewFileIcon(fileInfo.FilePath));
                    }
                }
                else
                {
                    // Show file icon for non-image files
                    stackPanel.Children.Add(CreatePreviewFileIcon(fileInfo.FilePath));
                }

                // File name and type
                var fileNameText = new TextBlock
                {
                    Text = fileInfo.FileName,
                    FontSize = 8,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(3),
                    TextWrapping = TextWrapping.Wrap
                };
                stackPanel.Children.Add(fileNameText);

                var fileTypeText = new TextBlock
                {
                    Text = GetPreviewFileTypeDescription(fileInfo.FilePath),
                    FontSize = 7,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(3, 0, 3, 3),
                    Foreground = Brushes.Gray
                };
                stackPanel.Children.Add(fileTypeText);

                border.Child = stackPanel;
                PreviewImagesContainer.Children.Add(border);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating preview file control: {ex.Message}");
            }
        }

        private FrameworkElement CreatePreviewFileIcon(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            var iconText = "📄";
            var backgroundColor = Brushes.LightGray;

            // Different icons for different file types
            switch (extension)
            {
                case ".pdf":
                    iconText = "📕";
                    backgroundColor = Brushes.LightCoral;
                    break;
                case ".doc":
                case ".docx":
                    iconText = "📘";
                    backgroundColor = Brushes.LightBlue;
                    break;
                case ".xls":
                case ".xlsx":
                    iconText = "📗";
                    backgroundColor = Brushes.LightGreen;
                    break;
                case ".txt":
                    iconText = "📝";
                    backgroundColor = Brushes.LightYellow;
                    break;
                default:
                    iconText = "📄";
                    backgroundColor = Brushes.LightGray;
                    break;
            }

            var border = new Border
            {
                Width = 180,
                Height = 150,
                Background = backgroundColor,
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(3)
            };

            var textBlock = new TextBlock
            {
                Text = iconText,
                FontSize = 50,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Foreground = Brushes.DarkGray
            };

            border.Child = textBlock;
            return border;
        }

        private string GetPreviewFileTypeDescription(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return extension switch
            {
                ".pdf" => "PDF",
                ".doc" or ".docx" => "Word",
                ".xls" or ".xlsx" => "Excel",
                ".txt" => "نص",
                ".jpg" or ".jpeg" => "JPEG",
                ".png" => "PNG",
                ".bmp" => "BMP",
                ".gif" => "GIF",
                ".tiff" => "TIFF",
                _ => extension.TrimStart('.')
            };
        }



        private bool IsImageFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return false;

            var extension = Path.GetExtension(filePath).ToLower();
            return extension == ".jpg" || extension == ".jpeg" || extension == ".png" ||
                   extension == ".bmp" || extension == ".gif" || extension == ".tiff" ||
                   extension == ".webp" || extension == ".ico";
        }

        public class ImageFileInfo
        {
            public string Title { get; set; } = string.Empty;
            public string FilePath { get; set; } = string.Empty;
            public string FileName { get; set; } = string.Empty;
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validation
                if (string.IsNullOrWhiteSpace(txtTitle.Text))
                {
                    MessageBox.Show("يرجى إدخال عنوان الخطاب", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtTitle.Focus();
                    return;
                }

                // Create or update letter
                if (_letter == null)
                {
                    _letter = new Letter { ProjectId = _projectId };
                }

                _letter.Title = txtTitle.Text.Trim();
                _letter.Notes = txtNotes.Text.Trim();
                _letter.LastModified = DateTime.Now;

                // Set date sent if selected
                if (dpDateSent.SelectedDate.HasValue)
                {
                    _letter.DateSent = dpDateSent.SelectedDate.Value;
                }

                // Set commitment if selected
                if (cmbCommitment.SelectedValue != null)
                {
                    _letter.CommitmentId = (int)cmbCommitment.SelectedValue;

                    // Debug info - يمكن حذف هذا السطر لاحقاً
                    var selectedCommitment = cmbCommitment.SelectedItem as Commitment;
                    System.Diagnostics.Debug.WriteLine($"Saving Letter with CommitmentId: {_letter.CommitmentId}, Selected Commitment: {selectedCommitment?.Title} (ID: {selectedCommitment?.Id})");
                }

                // Set selected invoices
                if (_selectedInvoiceIds.Any())
                {
                    _letter.SelectedInvoiceIds = string.Join(",", _selectedInvoiceIds);
                }

                // Copy file to project folder if selected
                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    try
                    {
                        var projectFolder = Path.Combine("Files", "Projects", _projectId.ToString(), "Letters");
                        Directory.CreateDirectory(projectFolder);

                        var fileName = $"{DateTime.Now:yyyyMMdd_HHmmss}_{Path.GetFileName(_selectedFilePath)}";
                        var destinationPath = Path.Combine(projectFolder, fileName);

                        File.Copy(_selectedFilePath, destinationPath, true);

                        _letter.LetterFilePath = destinationPath;
                        _letter.LetterFileName = fileName;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في نسخ الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Copy certificate file to project folder if selected
                if (!string.IsNullOrEmpty(_selectedCertificatePath))
                {
                    try
                    {
                        var projectFolder = Path.Combine("Files", "Projects", _projectId.ToString(), "Letters", "Certificates");
                        Directory.CreateDirectory(projectFolder);

                        var fileName = $"{DateTime.Now:yyyyMMdd_HHmmss}_Certificate_{Path.GetFileName(_selectedCertificatePath)}";
                        var destinationPath = Path.Combine(projectFolder, fileName);

                        File.Copy(_selectedCertificatePath, destinationPath, true);

                        _letter.CertificateFilePath = destinationPath;
                        _letter.CertificateFileName = fileName;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في نسخ ملف الشهادة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Save to database
                if (_letter.Id == 0)
                {
                    await _dataService.AddLetterAsync(_letter);
                }
                else
                {
                    await _dataService.UpdateLetterAsync(_letter);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الخطاب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
