using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using FinancialTracker.Helpers;

namespace FinancialTracker.Models
{
    [NotMapped]
    public class SiteReport
    {
        public int SiteId { get; set; }
        public string SiteName { get; set; } = string.Empty;
        public string SiteDisplayName { get; set; } = string.Empty;
        
        // Project statistics
        public int TotalProjects { get; set; }
        public int ActiveProjects { get; set; }
        public int CompletedProjects { get; set; }
        
        // Financial statistics
        public decimal TotalRevenue { get; set; }
        public decimal PaidRevenue { get; set; }
        public decimal PendingRevenue { get; set; }
        
        // Invoice statistics
        public int TotalInvoices { get; set; }
        public int PaidInvoices { get; set; }
        public int PendingInvoices { get; set; }
        public int PartiallyPaidInvoices { get; set; }
        
        // Commitment statistics
        public int TotalCommitments { get; set; }
        public int ActiveCommitments { get; set; }
        public decimal TotalCommitmentAmount { get; set; }
        public decimal InvoicedCommitmentAmount { get; set; }
        public decimal RemainingCommitmentAmount { get; set; }
        
        // Project details for this site
        public List<ProjectSiteDetail> ProjectDetails { get; set; } = new List<ProjectSiteDetail>();
        
        // Computed properties
        public decimal PaymentPercentage => TotalRevenue > 0 ? MathHelper.RoundCustom((PaidRevenue / TotalRevenue) * 100) : 0;
        public decimal InvoiceCompletionPercentage => TotalInvoices > 0 ? MathHelper.RoundCustom(((decimal)PaidInvoices / TotalInvoices) * 100) : 0;
        public decimal CommitmentCompletionPercentage => TotalCommitmentAmount > 0 ? MathHelper.RoundCustom((InvoicedCommitmentAmount / TotalCommitmentAmount) * 100) : 0;
    }

    [NotMapped]
    public class ProjectSiteDetail
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public string ProjectDescription { get; set; } = string.Empty;
        public string ProjectStatus { get; set; } = string.Empty;
        
        // Financial details for this project in this site
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        
        // Invoice details
        public int InvoiceCount { get; set; }
        public int PaidInvoiceCount { get; set; }
        public int PendingInvoiceCount { get; set; }
        
        // Commitment details
        public int CommitmentCount { get; set; }
        public decimal CommitmentAmount { get; set; }
        public decimal InvoicedCommitmentAmount { get; set; }
        public decimal RemainingCommitmentAmount { get; set; }
        
        // Dates
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime AssignedDate { get; set; }
        
        // Computed properties
        public decimal PaymentPercentage => TotalAmount > 0 ? MathHelper.RoundCustom((PaidAmount / TotalAmount) * 100) : 0;
        public decimal InvoiceCompletionPercentage => InvoiceCount > 0 ? MathHelper.RoundCustom(((decimal)PaidInvoiceCount / InvoiceCount) * 100) : 0;
        public decimal CommitmentCompletionPercentage => CommitmentAmount > 0 ? MathHelper.RoundCustom((InvoicedCommitmentAmount / CommitmentAmount) * 100) : 0;
    }

    [NotMapped]
    public class MultiSiteReport
    {
        public List<SiteReport> SiteReports { get; set; } = new List<SiteReport>();
        
        // Overall statistics across all sites
        public decimal TotalRevenueAllSites { get; set; }
        public decimal PaidRevenueAllSites { get; set; }
        public decimal PendingRevenueAllSites { get; set; }
        
        public int TotalInvoicesAllSites { get; set; }
        public int PaidInvoicesAllSites { get; set; }
        public int PendingInvoicesAllSites { get; set; }
        
        public int TotalCommitmentsAllSites { get; set; }
        public decimal TotalCommitmentAmountAllSites { get; set; }
        public decimal InvoicedCommitmentAmountAllSites { get; set; }
        public decimal RemainingCommitmentAmountAllSites { get; set; }
        
        // Project comparison across sites
        public List<ProjectMultiSiteComparison> ProjectComparisons { get; set; } = new List<ProjectMultiSiteComparison>();
        
        // Computed properties
        public decimal OverallPaymentPercentage => TotalRevenueAllSites > 0 ? MathHelper.RoundCustom((PaidRevenueAllSites / TotalRevenueAllSites) * 100) : 0;
        public decimal OverallInvoiceCompletionPercentage => TotalInvoicesAllSites > 0 ? MathHelper.RoundCustom(((decimal)PaidInvoicesAllSites / TotalInvoicesAllSites) * 100) : 0;
        public decimal OverallCommitmentCompletionPercentage => TotalCommitmentAmountAllSites > 0 ? MathHelper.RoundCustom((InvoicedCommitmentAmountAllSites / TotalCommitmentAmountAllSites) * 100) : 0;
    }

    [NotMapped]
    public class ProjectMultiSiteComparison
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public string ProjectDescription { get; set; } = string.Empty;
        
        // Financial totals across all sites
        public decimal TotalAmountAllSites { get; set; }
        public decimal PaidAmountAllSites { get; set; }
        public decimal RemainingAmountAllSites { get; set; }
        
        // Site-specific details
        public List<ProjectSiteDetail> SiteDetails { get; set; } = new List<ProjectSiteDetail>();
        
        // Computed properties
        public decimal OverallPaymentPercentage => TotalAmountAllSites > 0 ? MathHelper.RoundCustom((PaidAmountAllSites / TotalAmountAllSites) * 100) : 0;
        public int TotalSites => SiteDetails.Count;
        public decimal AverageAmountPerSite => TotalSites > 0 ? MathHelper.RoundCustom(TotalAmountAllSites / TotalSites) : 0;
    }
}
