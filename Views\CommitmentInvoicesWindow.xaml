<Window x:Class="FinancialTracker.CommitmentInvoicesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        Title="Commitment Invoices" 
        Height="600" 
        Width="1000"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FileDocument" Width="32" Height="32" Margin="0,0,8,0" Foreground="White"/>
                    <TextBlock x:Name="CommitmentTitleText" Text="Commitment Invoices" FontSize="20" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <Button Grid.Column="2" Content="Close" Style="{StaticResource MaterialDesignOutlinedButton}" 
                        Foreground="White" Margin="8,0" Click="CloseButton_Click"/>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Commitment Info -->
        <materialDesign:Card Grid.Row="1" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="Commitment Title" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="CommitmentTitleDetail" Text="" FontSize="16" Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1">
                    <TextBlock Text="Type" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="CommitmentTypeDetail" Text="" FontSize="16" Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2">
                    <TextBlock Text="Amount (USD)" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="CommitmentAmountDetail" Text="" FontSize="16" Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3">
                    <TextBlock Text="Total Related Invoices" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="TotalInvoicesDetail" Text="" FontSize="16" FontWeight="Bold" Margin="0,4,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Invoices List -->
        <Grid Grid.Row="2" Margin="16,8,16,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <Grid Grid.Row="0" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="Related Invoices" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                <Button Grid.Column="1" Content="Refresh" Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,8,0" Click="RefreshButton_Click"/>
                <Button Grid.Column="2" Content="Add New Invoice for this Commitment" Style="{StaticResource MaterialDesignRaisedButton}"
                        Click="AddInvoiceButton_Click"/>
            </Grid>
            
            <DataGrid Grid.Row="1" x:Name="InvoicesDataGrid" AutoGenerateColumns="False" CanUserAddRows="False" 
                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Invoice #" Binding="{Binding InvoiceNumber}" Width="120"/>
                    <DataGridTextColumn Header="Amount (USD)" Binding="{Binding AmountUSD, StringFormat='{}{0:C}'}" Width="120"/>
                    <DataGridTextColumn Header="Invoice Date" Binding="{Binding InvoiceDate, StringFormat='{}{0:yyyy-MM-dd}'}" Width="120"/>
                    <DataGridCheckBoxColumn Header="Paid" Binding="{Binding IsPaid}" Width="60"/>
                    <DataGridTextColumn Header="Paid Date" Binding="{Binding PaidDate, StringFormat='{}{0:yyyy-MM-dd}'}" Width="120"/>
                    <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="200"/>
                    <DataGridTextColumn Header="File" Binding="{Binding AttachedFileName}" Width="150"/>
                    <DataGridTemplateColumn Header="Actions" Width="250">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Margin="2" Padding="8,4" Click="EditInvoiceButton_Click"/>
                                    <Button Content="Copy" Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Margin="2" Padding="8,4" Click="CopyInvoiceButton_Click"/>
                                    <Button Content="Open File" Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Margin="2" Padding="8,4" Click="OpenInvoiceFileButton_Click"
                                            IsEnabled="{Binding AttachedFileName, Converter={StaticResource StringToVisibilityConverter}}"/>
                                    <Button Content="Remove" Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Margin="2" Padding="8,4" Click="DeleteInvoiceButton_Click"
                                            ToolTip="Remove invoice from this commitment"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>

        <!-- Summary -->
        <materialDesign:Card Grid.Row="3" Margin="16,0,16,16" Padding="16">
            <UniformGrid Columns="3">
                <StackPanel Margin="0,0,16,0">
                    <TextBlock Text="Total Invoice Amount" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="TotalAmountText" Text="$0.00" FontSize="18" FontWeight="Bold" Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel Margin="0,0,16,0">
                    <TextBlock Text="Paid Amount" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="PaidAmountText" Text="$0.00" FontSize="18" FontWeight="Bold" Foreground="Green" Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel>
                    <TextBlock Text="Unpaid Amount" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="UnpaidAmountText" Text="$0.00" FontSize="18" FontWeight="Bold" Foreground="Red" Margin="0,4,0,0"/>
                </StackPanel>
            </UniformGrid>
        </materialDesign:Card>
    </Grid>
</Window>
