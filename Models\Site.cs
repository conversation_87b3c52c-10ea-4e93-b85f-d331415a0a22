using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FinancialTracker.Helpers;

namespace FinancialTracker.Models
{
    public class Site
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(200)]
        public string DisplayName { get; set; } = string.Empty;

        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        [MaxLength(50)]
        public string Status { get; set; } = "Active";

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [MaxLength(200)]
        public string? ContactEmail { get; set; }

        [MaxLength(50)]
        public string? ContactPhone { get; set; }

        [MaxLength(500)]
        public string? Address { get; set; }

        // Navigation properties
        public virtual ICollection<ProjectSite> ProjectSites { get; set; } = new List<ProjectSite>();

        // Computed properties
        [NotMapped]
        public int ActiveProjectsCount => GetActiveProjectsCount();

        [NotMapped]
        public decimal TotalRevenue => GetTotalRevenue();

        [NotMapped]
        public decimal PaidRevenue => GetPaidRevenue();

        [NotMapped]
        public decimal PendingRevenue => TotalRevenue - PaidRevenue;

        private int GetActiveProjectsCount()
        {
            int count = 0;
            foreach (var projectSite in ProjectSites)
            {
                if (projectSite.Project?.Status == "Active")
                    count++;
            }
            return count;
        }

        private decimal GetTotalRevenue()
        {
            decimal total = 0;
            foreach (var projectSite in ProjectSites)
            {
                if (projectSite.Project != null)
                {
                    foreach (var invoice in projectSite.Project.Invoices)
                    {
                        if (invoice.SiteId == Id)
                            total += invoice.AmountUSD;
                    }
                }
            }
            return MathHelper.RoundCustom(total);
        }

        private decimal GetPaidRevenue()
        {
            decimal total = 0;
            foreach (var projectSite in ProjectSites)
            {
                if (projectSite.Project != null)
                {
                    foreach (var invoice in projectSite.Project.Invoices)
                    {
                        if (invoice.SiteId == Id)
                            total += invoice.PaidAmount;
                    }
                }
            }
            return MathHelper.RoundCustom(total);
        }
    }
}
