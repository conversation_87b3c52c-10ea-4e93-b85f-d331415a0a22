using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using FinancialTracker.Data;
using FinancialTracker.Models;
using FinancialTracker.Helpers;

namespace FinancialTracker.Services
{
    public class SiteReportService
    {
        private readonly FinancialContext _context;

        public SiteReportService(FinancialContext context)
        {
            _context = context;
        }

        public List<Site> GetAllSites()
        {
            return _context.Sites
                .Include(s => s.ProjectSites)
                    .ThenInclude(ps => ps.Project)
                        .ThenInclude(p => p.Invoices)
                .Include(s => s.ProjectSites)
                    .ThenInclude(ps => ps.Project)
                        .ThenInclude(p => p.Commitments)
                .OrderBy(s => s.Name)
                .ToList();
        }

        public Site? GetSiteById(int siteId)
        {
            return _context.Sites
                .Include(s => s.ProjectSites)
                    .ThenInclude(ps => ps.Project)
                        .ThenInclude(p => p.Invoices)
                .Include(s => s.ProjectSites)
                    .ThenInclude(ps => ps.Project)
                        .ThenInclude(p => p.Commitments)
                .FirstOrDefault(s => s.Id == siteId);
        }

        public SiteReport GenerateSiteReport(int siteId)
        {
            var site = GetSiteById(siteId);
            if (site == null) return new SiteReport();

            var report = new SiteReport
            {
                SiteId = site.Id,
                SiteName = site.Name,
                SiteDisplayName = site.DisplayName
            };

            // Calculate project statistics
            var projectSites = site.ProjectSites.ToList();
            report.TotalProjects = projectSites.Count;
            report.ActiveProjects = projectSites.Count(ps => ps.Project?.Status == "Active");
            report.CompletedProjects = projectSites.Count(ps => ps.Project?.Status == "Completed");

            // Calculate financial statistics
            var siteInvoices = GetInvoicesForSite(siteId);
            report.TotalRevenue = MathHelper.RoundCustom(siteInvoices.Sum(i => i.AmountUSD));
            report.PaidRevenue = MathHelper.RoundCustom(siteInvoices.Sum(i => i.PaidAmount));
            report.PendingRevenue = report.TotalRevenue - report.PaidRevenue;

            // Calculate invoice statistics
            report.TotalInvoices = siteInvoices.Count;
            report.PaidInvoices = siteInvoices.Count(i => i.IsFullyPaid);
            report.PartiallyPaidInvoices = siteInvoices.Count(i => i.IsPartiallyPaid);
            report.PendingInvoices = report.TotalInvoices - report.PaidInvoices - report.PartiallyPaidInvoices;

            // Calculate commitment statistics
            var siteCommitments = GetCommitmentsForSite(siteId);
            report.TotalCommitments = siteCommitments.Count;
            report.ActiveCommitments = siteCommitments.Count(c => c.IsActive);
            report.TotalCommitmentAmount = MathHelper.RoundCustom(siteCommitments.Sum(c => c.AmountUSD));
            report.InvoicedCommitmentAmount = MathHelper.RoundCustom(siteCommitments.Sum(c => c.TotalInvoicedAmount));
            report.RemainingCommitmentAmount = report.TotalCommitmentAmount - report.InvoicedCommitmentAmount;

            // Generate project details
            report.ProjectDetails = GenerateProjectSiteDetails(siteId);

            return report;
        }

        public MultiSiteReport GenerateMultiSiteReport()
        {
            var sites = GetAllSites();
            var report = new MultiSiteReport();

            foreach (var site in sites)
            {
                var siteReport = GenerateSiteReport(site.Id);
                report.SiteReports.Add(siteReport);
            }

            // Calculate overall statistics
            report.TotalRevenueAllSites = MathHelper.RoundCustom(report.SiteReports.Sum(sr => sr.TotalRevenue));
            report.PaidRevenueAllSites = MathHelper.RoundCustom(report.SiteReports.Sum(sr => sr.PaidRevenue));
            report.PendingRevenueAllSites = report.TotalRevenueAllSites - report.PaidRevenueAllSites;

            report.TotalInvoicesAllSites = report.SiteReports.Sum(sr => sr.TotalInvoices);
            report.PaidInvoicesAllSites = report.SiteReports.Sum(sr => sr.PaidInvoices);
            report.PendingInvoicesAllSites = report.TotalInvoicesAllSites - report.PaidInvoicesAllSites;

            report.TotalCommitmentsAllSites = report.SiteReports.Sum(sr => sr.TotalCommitments);
            report.TotalCommitmentAmountAllSites = MathHelper.RoundCustom(report.SiteReports.Sum(sr => sr.TotalCommitmentAmount));
            report.InvoicedCommitmentAmountAllSites = MathHelper.RoundCustom(report.SiteReports.Sum(sr => sr.InvoicedCommitmentAmount));
            report.RemainingCommitmentAmountAllSites = report.TotalCommitmentAmountAllSites - report.InvoicedCommitmentAmountAllSites;

            // Generate project comparisons
            report.ProjectComparisons = GenerateProjectMultiSiteComparisons();

            return report;
        }

        private List<Invoice> GetInvoicesForSite(int siteId)
        {
            return _context.Invoices
                .Include(i => i.Project)
                .Include(i => i.Site)
                .Where(i => i.SiteId == siteId)
                .ToList();
        }

        private List<Commitment> GetCommitmentsForSite(int siteId)
        {
            return _context.Commitments
                .Include(c => c.Project)
                .Include(c => c.Site)
                .Include(c => c.Invoices)
                .Where(c => c.SiteId == siteId)
                .ToList();
        }

        private List<ProjectSiteDetail> GenerateProjectSiteDetails(int siteId)
        {
            var projectSites = _context.ProjectSites
                .Include(ps => ps.Project)
                    .ThenInclude(p => p.Invoices)
                .Include(ps => ps.Project)
                    .ThenInclude(p => p.Commitments)
                .Where(ps => ps.SiteId == siteId)
                .ToList();

            var details = new List<ProjectSiteDetail>();

            foreach (var ps in projectSites)
            {
                if (ps.Project == null) continue;

                var projectInvoices = ps.Project.Invoices.Where(i => i.SiteId == siteId).ToList();
                var projectCommitments = ps.Project.Commitments.Where(c => c.SiteId == siteId).ToList();

                var detail = new ProjectSiteDetail
                {
                    ProjectId = ps.Project.Id,
                    ProjectName = ps.Project.Name,
                    ProjectDescription = ps.Project.Description,
                    ProjectStatus = ps.Project.Status,
                    
                    TotalAmount = MathHelper.RoundCustom(projectInvoices.Sum(i => i.AmountUSD)),
                    PaidAmount = MathHelper.RoundCustom(projectInvoices.Sum(i => i.PaidAmount)),
                    
                    InvoiceCount = projectInvoices.Count,
                    PaidInvoiceCount = projectInvoices.Count(i => i.IsFullyPaid),
                    PendingInvoiceCount = projectInvoices.Count(i => !i.IsFullyPaid),
                    
                    CommitmentCount = projectCommitments.Count,
                    CommitmentAmount = MathHelper.RoundCustom(projectCommitments.Sum(c => c.AmountUSD)),
                    InvoicedCommitmentAmount = MathHelper.RoundCustom(projectCommitments.Sum(c => c.TotalInvoicedAmount)),
                    
                    StartDate = ps.StartDate,
                    EndDate = ps.EndDate,
                    AssignedDate = ps.AssignedDate
                };

                detail.RemainingAmount = detail.TotalAmount - detail.PaidAmount;
                detail.RemainingCommitmentAmount = detail.CommitmentAmount - detail.InvoicedCommitmentAmount;

                details.Add(detail);
            }

            return details.OrderBy(d => d.ProjectName).ToList();
        }

        private List<ProjectMultiSiteComparison> GenerateProjectMultiSiteComparisons()
        {
            var projects = _context.Projects
                .Include(p => p.ProjectSites)
                    .ThenInclude(ps => ps.Site)
                .Include(p => p.Invoices)
                    .ThenInclude(i => i.Site)
                .Include(p => p.Commitments)
                    .ThenInclude(c => c.Site)
                .ToList();

            var comparisons = new List<ProjectMultiSiteComparison>();

            foreach (var project in projects)
            {
                var comparison = new ProjectMultiSiteComparison
                {
                    ProjectId = project.Id,
                    ProjectName = project.Name,
                    ProjectDescription = project.Description,
                    
                    TotalAmountAllSites = MathHelper.RoundCustom(project.Invoices.Sum(i => i.AmountUSD)),
                    PaidAmountAllSites = MathHelper.RoundCustom(project.Invoices.Sum(i => i.PaidAmount))
                };

                comparison.RemainingAmountAllSites = comparison.TotalAmountAllSites - comparison.PaidAmountAllSites;

                // Generate site details for this project
                foreach (var ps in project.ProjectSites)
                {
                    var siteInvoices = project.Invoices.Where(i => i.SiteId == ps.SiteId).ToList();
                    var siteCommitments = project.Commitments.Where(c => c.SiteId == ps.SiteId).ToList();

                    var siteDetail = new ProjectSiteDetail
                    {
                        ProjectId = project.Id,
                        ProjectName = project.Name,
                        ProjectDescription = project.Description,
                        ProjectStatus = project.Status,
                        
                        TotalAmount = MathHelper.RoundCustom(siteInvoices.Sum(i => i.AmountUSD)),
                        PaidAmount = MathHelper.RoundCustom(siteInvoices.Sum(i => i.PaidAmount)),
                        
                        InvoiceCount = siteInvoices.Count,
                        PaidInvoiceCount = siteInvoices.Count(i => i.IsFullyPaid),
                        PendingInvoiceCount = siteInvoices.Count(i => !i.IsFullyPaid),
                        
                        CommitmentCount = siteCommitments.Count,
                        CommitmentAmount = MathHelper.RoundCustom(siteCommitments.Sum(c => c.AmountUSD)),
                        InvoicedCommitmentAmount = MathHelper.RoundCustom(siteCommitments.Sum(c => c.TotalInvoicedAmount)),
                        
                        StartDate = ps.StartDate,
                        EndDate = ps.EndDate,
                        AssignedDate = ps.AssignedDate
                    };

                    siteDetail.RemainingAmount = siteDetail.TotalAmount - siteDetail.PaidAmount;
                    siteDetail.RemainingCommitmentAmount = siteDetail.CommitmentAmount - siteDetail.InvoicedCommitmentAmount;

                    comparison.SiteDetails.Add(siteDetail);
                }

                comparisons.Add(comparison);
            }

            return comparisons.OrderBy(c => c.ProjectName).ToList();
        }

        public bool AddSite(Site site)
        {
            try
            {
                _context.Sites.Add(site);
                _context.SaveChanges();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool UpdateSite(Site site)
        {
            try
            {
                _context.Sites.Update(site);
                _context.SaveChanges();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool AssignProjectToSite(int projectId, int siteId)
        {
            try
            {
                // Check if already assigned
                var existing = _context.ProjectSites
                    .FirstOrDefault(ps => ps.ProjectId == projectId && ps.SiteId == siteId);
                
                if (existing != null) return false; // Already assigned

                var projectSite = new ProjectSite
                {
                    ProjectId = projectId,
                    SiteId = siteId,
                    AssignedDate = DateTime.Now,
                    Status = "Active"
                };

                _context.ProjectSites.Add(projectSite);
                _context.SaveChanges();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool RemoveProjectFromSite(int projectId, int siteId)
        {
            try
            {
                var projectSite = _context.ProjectSites
                    .FirstOrDefault(ps => ps.ProjectId == projectId && ps.SiteId == siteId);
                
                if (projectSite == null) return false;

                _context.ProjectSites.Remove(projectSite);
                _context.SaveChanges();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
