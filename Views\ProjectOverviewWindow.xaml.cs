using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;
using LiveCharts;
using LiveCharts.Wpf;

namespace FinancialTracker
{
    public partial class ProjectOverviewWindow : Window
    {
        private readonly int _projectId;
        private Project _project;

        public ProjectOverviewWindow(int projectId)
        {
            InitializeComponent();
            _projectId = projectId;
            LoadProjectData();
            SetupCharts();
        }

        private async void LoadProjectData()
        {
            try
            {
                _project = await App.DataService.GetProjectByIdAsync(_projectId);
                if (_project == null)
                {
                    MessageBox.Show("Project not found.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    Close();
                    return;
                }

                UpdateProjectInfo();
                UpdateFinancialInputs();
                UpdateStatistics();
                UpdateCharts();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading project: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateProjectInfo()
        {
            ProjectNameText.Text = _project.Name;
            ProjectDescriptionText.Text = string.IsNullOrEmpty(_project.Description) ? "No description" : _project.Description;
            ProjectStatusText.Text = _project.Status;
            ProjectCreatedText.Text = _project.CreatedDate.ToString("yyyy-MM-dd");
            Title = $"Project Overview - {_project.Name}";
        }

        private void UpdateFinancialInputs()
        {
            PODatePicker.SelectedDate = _project.PODate;
            POAmountTextBox.Text = _project.POAmount > 0 ? _project.POAmount.ToString("F2") : "";
            TasksAmountTextBox.Text = _project.ManualTasksAmount > 0 ? _project.ManualTasksAmount.ToString("F2") : "";
            ServicesAmountTextBox.Text = _project.ManualServicesAmount > 0 ? _project.ManualServicesAmount.ToString("F2") : "";
        }

        private async void UpdateStatistics()
        {
            try
            {
                // Load fresh data
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _projectId).ToList();

                var allCommitments = await App.DataService.GetCommitmentsAsync();
                var projectCommitments = allCommitments.Where(c => c.ProjectId == _projectId).ToList();

                // Update displays
                TasksAmountDisplay.Text = $"${_project.TasksAmount:N0}";
                ServicesAmountDisplay.Text = $"${_project.ServicesAmount:N0}";
                TotalSpentDisplay.Text = $"${_project.SpentAmount:N0}";
                RemainingAmountDisplay.Text = $"${_project.RemainingFromPO:N0}";

                // Progress
                var completionPercentage = _project.POAmount > 0 ? (_project.SpentAmount / _project.POAmount) * 100 : 0;
                CompletionProgressBar.Value = Math.Min((double)completionPercentage, 100);
                CompletionPercentageDisplay.Text = $"{completionPercentage:F1}%";

                // Counts
                InvoicesCountDisplay.Text = projectInvoices.Count.ToString();
                InvoicesTotalDisplay.Text = $"${projectInvoices.Sum(i => i.AmountUSD):N0}";
                CommitmentsCountDisplay.Text = projectCommitments.Count.ToString();
                CommitmentsTotalDisplay.Text = $"${projectCommitments.Sum(c => c.AmountUSD):N0}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error updating statistics: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetupCharts()
        {
            // Setup pie chart for amount distribution
            AmountDistributionChart.Series = new SeriesCollection();
            
            // Setup progress chart
            ProgressChart.Series = new SeriesCollection();
            ProgressChart.AxisX.Add(new Axis
            {
                Title = "Category",
                Labels = new[] { "Tasks", "Services", "Spent", "Remaining" }
            });
            ProgressChart.AxisY.Add(new Axis
            {
                Title = "Amount ($)",
                LabelFormatter = value => value.ToString("C0")
            });
        }

        private void UpdateCharts()
        {
            try
            {
                // Update pie chart
                AmountDistributionChart.Series.Clear();
                AmountDistributionChart.Series.Add(new PieSeries
                {
                    Title = "Tasks",
                    Values = new ChartValues<decimal> { _project.TasksAmount },
                    DataLabels = true,
                    LabelPoint = chartPoint => $"${chartPoint.Y:N0}"
                });
                AmountDistributionChart.Series.Add(new PieSeries
                {
                    Title = "Services",
                    Values = new ChartValues<decimal> { _project.ServicesAmount },
                    DataLabels = true,
                    LabelPoint = chartPoint => $"${chartPoint.Y:N0}"
                });

                // Update progress chart
                ProgressChart.Series.Clear();
                ProgressChart.Series.Add(new ColumnSeries
                {
                    Title = "Financial Overview",
                    Values = new ChartValues<decimal> 
                    { 
                        _project.TasksAmount, 
                        _project.ServicesAmount, 
                        _project.SpentAmount, 
                        _project.RemainingFromPO 
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error updating charts: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveFinancialDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate and parse inputs
                if (!decimal.TryParse(POAmountTextBox.Text, out decimal poAmount) || poAmount < 0)
                {
                    MessageBox.Show("Please enter a valid PO amount.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(TasksAmountTextBox.Text, out decimal tasksAmount) || tasksAmount < 0)
                {
                    MessageBox.Show("Please enter a valid tasks amount.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(ServicesAmountTextBox.Text, out decimal servicesAmount) || servicesAmount < 0)
                {
                    MessageBox.Show("Please enter a valid services amount.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Update project
                _project.PODate = PODatePicker.SelectedDate;
                _project.POAmount = poAmount;
                _project.ManualTasksAmount = tasksAmount;
                _project.ManualServicesAmount = servicesAmount;

                await App.DataService.UpdateProjectAsync(_project);

                MessageBox.Show("Financial data saved successfully!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // Refresh displays
                UpdateStatistics();
                UpdateCharts();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving financial data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshDataButton_Click(object sender, RoutedEventArgs e)
        {
            LoadProjectData();
        }

        private void EditProjectButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProjectDialog(_project);
            if (dialog.ShowDialog() == true)
            {
                LoadProjectData();
            }
        }

        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new InvoiceDialog(null, _projectId);
            if (dialog.ShowDialog() == true)
            {
                UpdateStatistics();
                UpdateCharts();
            }
        }

        private void AddCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new CommitmentDialog(null, _projectId);
            if (dialog.ShowDialog() == true)
            {
                UpdateStatistics();
                UpdateCharts();
            }
        }

        private void ViewReportsButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement reports functionality
            MessageBox.Show("Reports functionality coming soon!", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
