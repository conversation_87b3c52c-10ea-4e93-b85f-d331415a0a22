using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using FinancialTracker.Helpers;

namespace FinancialTracker.Models
{
    public class Commitment
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        public int ProjectId { get; set; }

        [Required]
        public int SiteId { get; set; }

        [Required]
        public string Type { get; set; } = "مهمات";
        
        private decimal _amountUSD;

        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountUSD
        {
            get => MathHelper.RoundCustom(_amountUSD);
            set => _amountUSD = value;
        }
        
        [Column(TypeName = "decimal(10,4)")]
        public decimal ExchangeRate { get; set; } = 1.0m;
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? StartDate { get; set; }
        
        public DateTime? EndDate { get; set; }
        
        [MaxLength(500)]
        public string? AttachedFilePath { get; set; }
        
        [MaxLength(100)]
        public string? AttachedFileName { get; set; }
        
        public bool IsActive { get; set; } = true;

        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;

        [ForeignKey("SiteId")]
        public virtual Site Site { get; set; } = null!;

        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
        public virtual ICollection<Reply> Replies { get; set; } = new List<Reply>();

        // Computed property for UI
        [NotMapped]
        public int InvoicesCount => Invoices?.Count ?? 0;

        [NotMapped]
        public decimal TotalInvoicedAmount => MathHelper.RoundCustom(Invoices?.Sum(i => i.AmountUSD) ?? 0);

        [NotMapped]
        public decimal RemainingAmount => MathHelper.RoundCustom(AmountUSD - TotalInvoicedAmount);

        // خاصية لعرض النوع بشكل صحيح
        [NotMapped]
        public string TypeDisplay => GetTypeDisplay();

        private string GetTypeDisplay()
        {
            // إذا كان النوع رقم، نحوله إلى نص
            if (int.TryParse(Type, out int typeNumber))
            {
                return typeNumber switch
                {
                    1 => "مهمات سوفت وير",
                    2 => "مهمات هارد وير",
                    3 => "خدمات",
                    _ => "أخرى"
                };
            }

            // إذا كان النوع نص، نعيده كما هو
            return Type;
        }
    }
}
