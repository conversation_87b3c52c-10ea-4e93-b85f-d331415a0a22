using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;

namespace FinancialTracker.Services
{
    public interface IFileService
    {
        Task<string> SaveFileAsync(string sourceFilePath, string category, string fileName);
        Task<bool> DeleteFileAsync(string filePath);
        Task<bool> OpenFileAsync(string filePath);
        string GetFullPath(string relativePath);
        void EnsureDirectoriesExist();
        string SelectFile(string filter = "All Files (*.*)|*.*");
    }

    public class FileService : IFileService
    {
        private readonly string _baseDirectory;
        private readonly string _dataDirectory;
        private readonly string _invoicesDirectory;
        private readonly string _commitmentsDirectory;
        private readonly string _repliesDirectory;

        public FileService()
        {
            _baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _dataDirectory = Path.Combine(_baseDirectory, "data");
            _invoicesDirectory = Path.Combine(_dataDirectory, "invoices");
            _commitmentsDirectory = Path.Combine(_dataDirectory, "commitments");
            _repliesDirectory = Path.Combine(_dataDirectory, "replies");

            EnsureDirectoriesExist();
        }

        public void EnsureDirectoriesExist()
        {
            Directory.CreateDirectory(_dataDirectory);
            Directory.CreateDirectory(_invoicesDirectory);
            Directory.CreateDirectory(_commitmentsDirectory);
            Directory.CreateDirectory(_repliesDirectory);
        }

        public string SelectFile(string filter = "All Files (*.*)|*.*")
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = filter,
                Title = "Select File"
            };

            return openFileDialog.ShowDialog() == true ? openFileDialog.FileName : string.Empty;
        }

        public async Task<string> SaveFileAsync(string sourceFilePath, string category, string fileName)
        {
            if (!File.Exists(sourceFilePath))
                throw new FileNotFoundException("Source file not found", sourceFilePath);

            string targetDirectory = category.ToLower() switch
            {
                "invoice" => _invoicesDirectory,
                "commitment" => _commitmentsDirectory,
                "reply" => _repliesDirectory,
                _ => throw new ArgumentException("Invalid category", nameof(category))
            };

            // Generate unique filename if file already exists
            string targetFilePath = Path.Combine(targetDirectory, fileName);
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            string extension = Path.GetExtension(fileName);
            int counter = 1;

            while (File.Exists(targetFilePath))
            {
                string newFileName = $"{fileNameWithoutExtension}_{counter}{extension}";
                targetFilePath = Path.Combine(targetDirectory, newFileName);
                counter++;
            }

            await Task.Run(() => File.Copy(sourceFilePath, targetFilePath, true));
            
            // Return relative path from base directory
            return Path.GetRelativePath(_baseDirectory, targetFilePath);
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                string fullPath = GetFullPath(filePath);
                if (File.Exists(fullPath))
                {
                    await Task.Run(() => File.Delete(fullPath));
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> OpenFileAsync(string filePath)
        {
            try
            {
                string fullPath = GetFullPath(filePath);
                if (File.Exists(fullPath))
                {
                    await Task.Run(() =>
                    {
                        var processStartInfo = new ProcessStartInfo
                        {
                            FileName = fullPath,
                            UseShellExecute = true
                        };
                        Process.Start(processStartInfo);
                    });
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public string GetFullPath(string relativePath)
        {
            if (string.IsNullOrEmpty(relativePath))
                return string.Empty;
                
            if (Path.IsPathRooted(relativePath))
                return relativePath;
            
            return Path.Combine(_baseDirectory, relativePath);
        }
    }
}
