<Window x:Class="FinancialTracker.ProjectDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        Title="Project Details" 
        Height="900" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FolderOpen" Width="32" Height="32" Margin="0,0,8,0" Foreground="White"/>
                    <TextBlock x:Name="ProjectNameText" Text="Project Details" FontSize="20" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Content="Edit Project" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Foreground="White" Margin="8,0" Click="EditProjectButton_Click"/>
                    <Button Content="Close" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Foreground="White" Margin="8,0" Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

                <!-- Project Info Card - Compact -->
                <materialDesign:Card Grid.Row="0" Margin="0,0,0,8" Padding="12">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,8,0">
                            <TextBlock Text="Project" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                            <TextBlock x:Name="ProjectNameDetail" Text="" FontSize="14" FontWeight="Medium"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Margin="0,0,8,0">
                            <TextBlock Text="Status" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                            <TextBlock x:Name="ProjectStatusDetail" Text="" FontSize="14"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" Margin="0,0,8,0">
                            <TextBlock Text="Created" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                            <TextBlock x:Name="ProjectCreatedDetail" Text="" FontSize="14"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3">
                            <TextBlock Text="Description" FontWeight="Medium" Opacity="0.7" FontSize="11"/>
                            <TextBlock x:Name="ProjectDescriptionDetail" Text="" FontSize="14" TextWrapping="Wrap"/>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>

                <!-- Statistics Cards - Dashboard Style -->
                <Grid Grid.Row="1" Margin="0,0,0,8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- First Row - Main Counts -->
                    <Grid Grid.Row="0" Margin="0,0,0,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="8" Background="#E3F2FD" MinHeight="70">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="عدد الخطابات" Opacity="0.7" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="11"/>
                                <TextBlock x:Name="LettersCountText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#1976D2" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,2"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="1" Margin="4" Padding="8" Background="#FFF3E0" MinHeight="70">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="عدد الارتباطات" Opacity="0.7" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="11"/>
                                <TextBlock x:Name="CommitmentsCountText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#F57C00" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,2"/>
                                <TextBlock x:Name="CommitmentsTotalText" Text="$0" FontSize="13" FontWeight="Medium" Opacity="0.9" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="2" Margin="4" Padding="8" Background="#E8F5E8" MinHeight="70">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="عدد الفواتير" Opacity="0.7" FontWeight="Medium" HorizontalAlignment="Center" TextAlignment="Center" FontSize="11"/>
                                <TextBlock x:Name="InvoicesCountText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#388E3C" HorizontalAlignment="Center" TextAlignment="Center" Margin="0,2"/>
                                <TextBlock x:Name="InvoicesTotalText" Text="$0" FontSize="13" FontWeight="Medium" Opacity="0.9" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>

                    <!-- Second Row - Financial Details -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="8" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="إجمالي الفواتير" Opacity="0.7" HorizontalAlignment="Center" TextAlignment="Center" FontSize="10"/>
                                <TextBlock x:Name="TotalInvoiceAmountText" Text="$0.00" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="1" Margin="4" Padding="8" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="المبلغ المدفوع" Opacity="0.7" HorizontalAlignment="Center" TextAlignment="Center" FontSize="10"/>
                                <TextBlock x:Name="PaidAmountText" Text="$0.00" FontSize="16" FontWeight="Bold" Foreground="Green" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="2" Margin="4" Padding="8" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="المبلغ المتبقي" Opacity="0.7" HorizontalAlignment="Center" TextAlignment="Center" FontSize="10"/>
                                <TextBlock x:Name="UnpaidAmountText" Text="$0.00" FontSize="16" FontWeight="Bold" Foreground="Red" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="3" Margin="4" Padding="8" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="تفصيل الارتباطات" Opacity="0.7" HorizontalAlignment="Center" TextAlignment="Center" FontSize="10"/>
                                <TextBlock x:Name="CommitmentsBreakdownText" Text="مهمات: 0 | خدمات: 0" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </Grid>

                <!-- Tabs -->
                <TabControl Grid.Row="2" Style="{StaticResource MaterialDesignTabControl}">
                    <!-- Invoices Tab -->
                    <TabItem Header="الفواتير">
                        <Grid Margin="0,16,0,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Invoice Controls -->
                            <materialDesign:Card Grid.Row="0" Margin="0,0,0,16" Padding="0">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="12,8">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                                <materialDesign:PackIcon Kind="FileDocument" Width="18" Height="18" Margin="0,0,6,0"/>
                                                <TextBlock Text="فلترة الفواتير" FontSize="13" FontWeight="Medium"/>
                                            </StackPanel>

                                            <Button Grid.Column="2" Content="إضافة فاتورة جديدة" Style="{StaticResource MaterialDesignRaisedButton}"
                                                    FontSize="12" Padding="12,6" Click="AddInvoiceButton_Click"/>
                                        </Grid>
                                    </materialDesign:ColorZone>

                                    <!-- Filter Controls -->
                                    <Grid Grid.Row="1" Margin="12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                            <TextBlock Text="البحث في الفواتير" FontSize="11" FontWeight="Medium" Margin="0,0,0,4" Opacity="0.8"/>
                                            <Grid>
                                                <TextBox x:Name="InvoiceSearchTextBox" materialDesign:HintAssist.Hint="رقم الفاتورة أو الوصف..."
                                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                         Padding="35,8,8,8" MinWidth="300"
                                                         TextChanged="InvoiceSearchTextBox_TextChanged"/>
                                                <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16"
                                                                       HorizontalAlignment="Left" VerticalAlignment="Center"
                                                                       Margin="10,0,0,0" Opacity="0.6"/>
                                            </Grid>
                                        </StackPanel>

                                        <Button Grid.Column="1" x:Name="ClearInvoiceFilterButton"
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                Padding="12,8" FontSize="11" VerticalAlignment="Bottom"
                                                Click="ClearInvoiceFilterButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="FilterRemove" Width="14" Height="14" Margin="0,0,4,0"/>
                                                <TextBlock Text="مسح"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>

                            <DataGrid Grid.Row="2" x:Name="InvoicesDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="*"/>
                                    <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="*"/>
                                    <DataGridTextColumn Header="المبلغ" Binding="{Binding AmountUSD, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="المدفوع" Binding="{Binding PaidAmount, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="المتبقي" Binding="{Binding RemainingAmount, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="الحالة" Binding="{Binding PaymentStatus}" Width="*"/>
                                    <DataGridTextColumn Header="التاريخ" Binding="{Binding InvoiceDate, StringFormat='{}{0:yyyy-MM-dd}'}" Width="*"/>
                                    <DataGridTextColumn Header="الارتباط" Binding="{Binding Commitment.Title}" Width="*"/>
                                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                                    <DataGridTemplateColumn Header="الإجراءات" Width="250">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="EditInvoiceButton_Click"/>
                                                    <Button Content="Copy" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="CopyInvoiceButton_Click"/>
                                                    <Button Content="الخطاب" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="OpenInvoiceFileButton_Click"
                                                            IsEnabled="{Binding AttachedFileName, Converter={StaticResource StringToVisibilityConverter}}"/>
                                                    <Button Content="Delete" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="DeleteInvoiceButton_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>

                    <!-- Commitments Tab -->
                    <TabItem Header="الارتباطات">
                        <Grid Margin="0,16,0,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Commitment Controls -->
                            <materialDesign:Card Grid.Row="0" Margin="0,0,0,16" Padding="0">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="12,8">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                                <materialDesign:PackIcon Kind="Link" Width="18" Height="18" Margin="0,0,6,0"/>
                                                <TextBlock Text="فلترة الارتباطات" FontSize="13" FontWeight="Medium"/>
                                            </StackPanel>

                                            <Button Grid.Column="2" Content="إضافة ارتباط جديد" Style="{StaticResource MaterialDesignRaisedButton}"
                                                    FontSize="12" Padding="12,6" Click="AddCommitmentButton_Click"/>
                                        </Grid>
                                    </materialDesign:ColorZone>

                                    <!-- Filter Controls -->
                                    <Grid Grid.Row="1" Margin="12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                            <TextBlock Text="البحث في الارتباطات" FontSize="11" FontWeight="Medium" Margin="0,0,0,4" Opacity="0.8"/>
                                            <Grid>
                                                <TextBox x:Name="CommitmentSearchTextBox" materialDesign:HintAssist.Hint="عنوان الارتباط أو الوصف..."
                                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                         Padding="35,8,8,8" MinWidth="300"
                                                         TextChanged="CommitmentSearchTextBox_TextChanged"/>
                                                <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16"
                                                                       HorizontalAlignment="Left" VerticalAlignment="Center"
                                                                       Margin="10,0,0,0" Opacity="0.6"/>
                                            </Grid>
                                        </StackPanel>

                                        <Button Grid.Column="1" x:Name="ClearCommitmentFilterButton"
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                Padding="12,8" FontSize="11" VerticalAlignment="Bottom"
                                                Click="ClearCommitmentFilterButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="FilterRemove" Width="14" Height="14" Margin="0,0,4,0"/>
                                                <TextBlock Text="مسح"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>

                            <DataGrid Grid.Row="2" x:Name="CommitmentsDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="العنوان" Binding="{Binding Title}" Width="*"/>
                                    <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="*"/>
                                    <DataGridTextColumn Header="المبلغ الكلي" Binding="{Binding AmountUSD, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="المبلغ المفوتر" Binding="{Binding TotalInvoicedAmount, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="المبلغ المتبقي" Binding="{Binding RemainingAmount, StringFormat='{}{0:C0}'}" Width="*"/>
                                    <DataGridTextColumn Header="سعر الصرف" Binding="{Binding ExchangeRate}" Width="*"/>
                                    <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat='{}{0:yyyy-MM-dd}'}" Width="*"/>
                                    <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="60"/>
                                    <DataGridTextColumn Header="عدد الفواتير" Binding="{Binding InvoicesCount}" Width="*"/>
                                    <DataGridTemplateColumn Header="الإجراءات" Width="270">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="EditCommitmentButton_Click"/>
                                                    <Button Content="Copy" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="CopyCommitmentButton_Click"/>
                                                    <Button Content="Invoices" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="ViewCommitmentInvoicesButton_Click"/>
                                                    <Button Content="File" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="OpenCommitmentFileButton_Click"
                                                            IsEnabled="{Binding AttachedFileName, Converter={StaticResource StringToVisibilityConverter}}"/>
                                                    <Button Content="Delete" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="DeleteCommitmentButton_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>

                    <!-- Letters Tab -->
                    <TabItem Header="الخطابات">
                        <Grid Margin="0,16,0,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <materialDesign:Card Grid.Row="0" Margin="0,0,0,16" Padding="16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                        <materialDesign:PackIcon Kind="Email" Width="18" Height="18" Margin="0,0,6,0"/>
                                        <TextBlock Text="إدارة الخطابات" FontSize="13" FontWeight="Medium"/>
                                    </StackPanel>

                                    <Button Grid.Column="1" Content="إضافة خطاب جديد" Style="{StaticResource MaterialDesignRaisedButton}"
                                            FontSize="12" Padding="12,6" Click="AddLetterButton_Click"/>
                                </Grid>
                            </materialDesign:Card>

                            <materialDesign:Card Grid.Row="1" Margin="0,0,0,16" Padding="16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                        <TextBlock Text="البحث في الخطابات" FontSize="11" FontWeight="Medium" Margin="0,0,0,4" Opacity="0.8"/>
                                        <Grid>
                                            <TextBox x:Name="LetterSearchTextBox" materialDesign:HintAssist.Hint="عنوان الخطاب..."
                                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                     Padding="35,8,8,8" MinWidth="300"
                                                     TextChanged="LetterSearchTextBox_TextChanged"/>
                                            <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16"
                                                                   HorizontalAlignment="Left" VerticalAlignment="Center"
                                                                   Margin="10,0,0,0" Opacity="0.6"/>
                                        </Grid>
                                    </StackPanel>

                                    <Button Grid.Column="1" Style="{StaticResource MaterialDesignOutlinedButton}"
                                            FontSize="12" Padding="12,6" Click="RefreshLettersButton_Click">
                                        <Button.Content>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Refresh" Width="14" Height="14" Margin="0,0,4,0"/>
                                                <TextBlock Text="تحديث"/>
                                            </StackPanel>
                                        </Button.Content>
                                    </Button>
                                </Grid>
                            </materialDesign:Card>

                            <DataGrid Grid.Row="2" x:Name="LettersDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="العنوان" Binding="{Binding Title}" Width="*"/>
                                    <DataGridTextColumn Header="الارتباط" Binding="{Binding Commitment.Title}" Width="*"/>
                                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                                    <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding DateCreated, StringFormat='{}{0:yyyy-MM-dd}'}" Width="120"/>
                                    <DataGridTextColumn Header="تاريخ الإرسال" Binding="{Binding DateSent, StringFormat='{}{0:yyyy-MM-dd}'}" Width="120"/>
                                    <DataGridTextColumn Header="اسم الملف" Binding="{Binding LetterFileName}" Width="150"/>
                                    <DataGridTemplateColumn Header="الإجراءات" Width="200">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Content="تعديل" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="EditLetterButton_Click"/>
                                                    <Button Content="عرض" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="ViewLetterButton_Click"/>
                                                    <Button Content="حذف" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="6,2" FontSize="11" Click="DeleteLetterButton_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>
                </TabControl>
        </Grid>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,4">
            <Grid>
                <TextBlock x:Name="StatusText" Text="Ready" VerticalAlignment="Center"/>
                <TextBlock Text="Project Details View" HorizontalAlignment="Right" VerticalAlignment="Center"/>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>
