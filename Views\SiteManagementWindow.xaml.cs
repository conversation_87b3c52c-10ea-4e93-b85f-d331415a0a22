using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Effects;
using FinancialTracker.Data;
using FinancialTracker.Models;
using FinancialTracker.Services;

namespace FinancialTracker.Views
{
    public partial class SiteManagementWindow : Window
    {
        private readonly FinancialContext _context;
        private readonly SiteReportService _reportService;
        private List<Site> _sites;

        public SiteManagementWindow()
        {
            InitializeComponent();
            _context = new FinancialContext();
            _reportService = new SiteReportService(_context);
            _sites = new List<Site>();
            LoadData();
        }

        private void LoadData()
        {
            try
            {
                _sites = _reportService.GetAllSites();
                lstSites.ItemsSource = _sites;
                cmbSiteFilter.ItemsSource = _sites;
                
                txtStatus.Text = $"تم تحميل {_sites.Count} موقع";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnAddSite_Click(object sender, RoutedEventArgs e)
        {
            // For now, show a simple input dialog
            var name = Microsoft.VisualBasic.Interaction.InputBox("أدخل اسم الموقع:", "إضافة موقع جديد", "");
            if (string.IsNullOrWhiteSpace(name)) return;

            var displayName = Microsoft.VisualBasic.Interaction.InputBox("أدخل الاسم المعروض للموقع:", "إضافة موقع جديد", name);
            if (string.IsNullOrWhiteSpace(displayName)) displayName = name;

            var description = Microsoft.VisualBasic.Interaction.InputBox("أدخل وصف الموقع (اختياري):", "إضافة موقع جديد", "");

            var newSite = new Site
            {
                Name = name.Trim(),
                DisplayName = displayName.Trim(),
                Description = description?.Trim() ?? "",
                Status = "Active",
                CreatedDate = DateTime.Now
            };

            if (_reportService.AddSite(newSite))
            {
                LoadData();
                txtStatus.Text = "تم إضافة الموقع بنجاح";
            }
            else
            {
                MessageBox.Show("فشل في إضافة الموقع", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditSite_Click(object sender, RoutedEventArgs e)
        {
            var selectedSite = lstSites.SelectedItem as Site;
            if (selectedSite == null)
            {
                MessageBox.Show("يرجى اختيار موقع للتعديل", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var name = Microsoft.VisualBasic.Interaction.InputBox("أدخل اسم الموقع:", "تعديل الموقع", selectedSite.Name);
            if (string.IsNullOrWhiteSpace(name)) return;

            var displayName = Microsoft.VisualBasic.Interaction.InputBox("أدخل الاسم المعروض للموقع:", "تعديل الموقع", selectedSite.DisplayName);
            if (string.IsNullOrWhiteSpace(displayName)) displayName = name;

            var description = Microsoft.VisualBasic.Interaction.InputBox("أدخل وصف الموقع (اختياري):", "تعديل الموقع", selectedSite.Description);

            selectedSite.Name = name.Trim();
            selectedSite.DisplayName = displayName.Trim();
            selectedSite.Description = description?.Trim() ?? "";

            if (_reportService.UpdateSite(selectedSite))
            {
                LoadData();
                txtStatus.Text = "تم تحديث الموقع بنجاح";
            }
            else
            {
                MessageBox.Show("فشل في تحديث الموقع", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }

        private void LstSites_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var selectedSite = lstSites.SelectedItem as Site;
            btnEditSite.IsEnabled = selectedSite != null;

            if (selectedSite != null)
            {
                DisplaySiteDetails(selectedSite);
            }
            else
            {
                ClearSiteDetails();
            }
        }

        private void DisplaySiteDetails(Site site)
        {
            pnlSiteDetails.Children.Clear();

            // Site Information
            var infoGroup = CreateGroupBox("معلومات الموقع");
            var infoPanel = new StackPanel();

            infoPanel.Children.Add(CreateInfoRow("الاسم:", site.Name));
            infoPanel.Children.Add(CreateInfoRow("الاسم المعروض:", site.DisplayName));
            infoPanel.Children.Add(CreateInfoRow("الوصف:", site.Description));
            infoPanel.Children.Add(CreateInfoRow("الحالة:", site.Status));
            infoPanel.Children.Add(CreateInfoRow("تاريخ الإنشاء:", site.CreatedDate.ToString("yyyy/MM/dd")));

            if (!string.IsNullOrEmpty(site.ContactEmail))
                infoPanel.Children.Add(CreateInfoRow("البريد الإلكتروني:", site.ContactEmail));
            if (!string.IsNullOrEmpty(site.ContactPhone))
                infoPanel.Children.Add(CreateInfoRow("الهاتف:", site.ContactPhone));
            if (!string.IsNullOrEmpty(site.Address))
                infoPanel.Children.Add(CreateInfoRow("العنوان:", site.Address));

            infoGroup.Content = infoPanel;
            pnlSiteDetails.Children.Add(infoGroup);

            // Statistics
            var statsGroup = CreateGroupBox("الإحصائيات");
            var statsPanel = new StackPanel();

            statsPanel.Children.Add(CreateInfoRow("عدد المشاريع النشطة:", site.ActiveProjectsCount.ToString()));
            statsPanel.Children.Add(CreateInfoRow("إجمالي الإيرادات:", $"{site.TotalRevenue:F2} USD"));
            statsPanel.Children.Add(CreateInfoRow("الإيرادات المدفوعة:", $"{site.PaidRevenue:F2} USD"));
            statsPanel.Children.Add(CreateInfoRow("الإيرادات المعلقة:", $"{site.PendingRevenue:F2} USD"));

            statsGroup.Content = statsPanel;
            pnlSiteDetails.Children.Add(statsGroup);

            // Projects List
            var projectsGroup = CreateGroupBox("المشاريع المرتبطة");
            var projectsPanel = new StackPanel();

            foreach (var ps in site.ProjectSites.OrderBy(ps => ps.Project?.Name))
            {
                if (ps.Project == null) continue;

                var projectBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(245, 245, 245)),
                    CornerRadius = new CornerRadius(6),
                    Padding = new Thickness(12),
                    Margin = new Thickness(0, 4, 0, 4),
                    BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
                    BorderThickness = new Thickness(1)
                };

                var projectPanel = new StackPanel();

                projectPanel.Children.Add(new TextBlock
                {
                    Text = ps.Project.Name,
                    FontWeight = FontWeights.Bold,
                    FontSize = 14,
                    Foreground = new SolidColorBrush(Color.FromRgb(63, 81, 181)),
                    Margin = new Thickness(0, 0, 0, 4)
                });
                projectPanel.Children.Add(new TextBlock
                {
                    Text = ps.Project.Description,
                    TextWrapping = TextWrapping.Wrap,
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(97, 97, 97)),
                    Margin = new Thickness(0, 0, 0, 4)
                });
                projectPanel.Children.Add(new TextBlock
                {
                    Text = $"الحالة: {ps.Status}",
                    FontSize = 11,
                    FontWeight = FontWeights.Medium,
                    Foreground = ps.Status == "Active" ? Brushes.Green : Brushes.Orange
                });

                projectBorder.Child = projectPanel;
                projectsPanel.Children.Add(projectBorder);
            }

            if (projectsPanel.Children.Count == 0)
            {
                projectsPanel.Children.Add(new TextBlock
                {
                    Text = "لا توجد مشاريع مرتبطة بهذا الموقع",
                    FontStyle = FontStyles.Italic,
                    Foreground = Brushes.Gray,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 16, 0, 16)
                });
            }

            projectsGroup.Content = projectsPanel;
            pnlSiteDetails.Children.Add(projectsGroup);
        }

        private void ClearSiteDetails()
        {
            pnlSiteDetails.Children.Clear();
            pnlSiteDetails.Children.Add(new TextBlock 
            { 
                Text = "اختر موقعاً من القائمة لعرض التفاصيل", 
                HorizontalAlignment = HorizontalAlignment.Center, 
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 14, 
                Foreground = Brushes.Gray 
            });
        }

        private GroupBox CreateGroupBox(string header)
        {
            var groupBox = new GroupBox
            {
                Header = header,
                Margin = new Thickness(0, 0, 0, 16),
                Padding = new Thickness(16),
                Background = new SolidColorBrush(Color.FromRgb(250, 250, 250)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
                BorderThickness = new Thickness(1),
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(63, 81, 181))
            };

            return groupBox;
        }

        private StackPanel CreateInfoRow(string label, string value)
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 4, 0, 4)
            };

            panel.Children.Add(new TextBlock
            {
                Text = label,
                FontWeight = FontWeights.Medium,
                Width = 150,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(97, 97, 97))
            });
            panel.Children.Add(new TextBlock
            {
                Text = value,
                TextWrapping = TextWrapping.Wrap,
                FontSize = 14,
                FontWeight = FontWeights.Normal,
                Foreground = new SolidColorBrush(Color.FromRgb(33, 33, 33))
            });

            return panel;
        }

        private void CmbSiteFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Enable report generation button when a site is selected
            btnGenerateSiteReport.IsEnabled = cmbSiteFilter.SelectedItem != null;
        }

        private void BtnGenerateSiteReport_Click(object sender, RoutedEventArgs e)
        {
            var selectedSite = cmbSiteFilter.SelectedItem as Site;
            if (selectedSite == null) return;

            try
            {
                var report = _reportService.GenerateSiteReport(selectedSite.Id);
                DisplaySiteReport(report);
                txtStatus.Text = $"تم إنشاء تقرير الموقع: {selectedSite.DisplayName}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DisplaySiteReport(SiteReport report)
        {
            pnlReportContent.Children.Clear();

            // Report Header
            var headerText = new TextBlock
            {
                Text = $"تقرير الموقع: {report.SiteDisplayName}",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            pnlReportContent.Children.Add(headerText);

            // Financial Summary
            var financialGroup = CreateGroupBox("الملخص المالي");
            var financialPanel = new StackPanel();
            
            financialPanel.Children.Add(CreateInfoRow("إجمالي الإيرادات:", $"{report.TotalRevenue:F2} USD"));
            financialPanel.Children.Add(CreateInfoRow("الإيرادات المدفوعة:", $"{report.PaidRevenue:F2} USD"));
            financialPanel.Children.Add(CreateInfoRow("الإيرادات المعلقة:", $"{report.PendingRevenue:F2} USD"));
            financialPanel.Children.Add(CreateInfoRow("نسبة الدفع:", $"{report.PaymentPercentage:F1}%"));

            financialGroup.Content = financialPanel;
            pnlReportContent.Children.Add(financialGroup);

            // Project Summary
            var projectGroup = CreateGroupBox("ملخص المشاريع");
            var projectPanel = new StackPanel();
            
            projectPanel.Children.Add(CreateInfoRow("إجمالي المشاريع:", report.TotalProjects.ToString()));
            projectPanel.Children.Add(CreateInfoRow("المشاريع النشطة:", report.ActiveProjects.ToString()));
            projectPanel.Children.Add(CreateInfoRow("المشاريع المكتملة:", report.CompletedProjects.ToString()));

            projectGroup.Content = projectPanel;
            pnlReportContent.Children.Add(projectGroup);

            // Invoice Summary
            var invoiceGroup = CreateGroupBox("ملخص الفواتير");
            var invoicePanel = new StackPanel();
            
            invoicePanel.Children.Add(CreateInfoRow("إجمالي الفواتير:", report.TotalInvoices.ToString()));
            invoicePanel.Children.Add(CreateInfoRow("الفواتير المدفوعة:", report.PaidInvoices.ToString()));
            invoicePanel.Children.Add(CreateInfoRow("الفواتير المعلقة:", report.PendingInvoices.ToString()));
            invoicePanel.Children.Add(CreateInfoRow("الفواتير المدفوعة جزئياً:", report.PartiallyPaidInvoices.ToString()));
            invoicePanel.Children.Add(CreateInfoRow("نسبة إكمال الفواتير:", $"{report.InvoiceCompletionPercentage:F1}%"));

            invoiceGroup.Content = invoicePanel;
            pnlReportContent.Children.Add(invoiceGroup);

            // Commitment Summary
            var commitmentGroup = CreateGroupBox("ملخص الالتزامات");
            var commitmentPanel = new StackPanel();
            
            commitmentPanel.Children.Add(CreateInfoRow("إجمالي الالتزامات:", report.TotalCommitments.ToString()));
            commitmentPanel.Children.Add(CreateInfoRow("الالتزامات النشطة:", report.ActiveCommitments.ToString()));
            commitmentPanel.Children.Add(CreateInfoRow("مبلغ الالتزامات:", $"{report.TotalCommitmentAmount:F2} USD"));
            commitmentPanel.Children.Add(CreateInfoRow("المبلغ المفوتر:", $"{report.InvoicedCommitmentAmount:F2} USD"));
            commitmentPanel.Children.Add(CreateInfoRow("المبلغ المتبقي:", $"{report.RemainingCommitmentAmount:F2} USD"));
            commitmentPanel.Children.Add(CreateInfoRow("نسبة إكمال الالتزامات:", $"{report.CommitmentCompletionPercentage:F1}%"));

            commitmentGroup.Content = commitmentPanel;
            pnlReportContent.Children.Add(commitmentGroup);
        }

        private void BtnGenerateReport_Click(object sender, RoutedEventArgs e)
        {
            tabControl.SelectedIndex = 2; // Switch to Multi-Site Comparison tab
            BtnGenerateMultiSiteReport_Click(sender, e);
        }

        private void BtnGenerateMultiSiteReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var multiReport = _reportService.GenerateMultiSiteReport();
                DisplayMultiSiteReport(multiReport);
                txtStatus.Text = "تم إنشاء التقرير الشامل بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير الشامل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DisplayMultiSiteReport(MultiSiteReport report)
        {
            pnlMultiSiteReport.Children.Clear();

            // Report Header
            var headerText = new TextBlock
            {
                Text = "التقرير الشامل - مقارنة جميع المواقع",
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            pnlMultiSiteReport.Children.Add(headerText);

            // Overall Summary
            var overallGroup = CreateGroupBox("الملخص العام");
            var overallPanel = new StackPanel();
            
            overallPanel.Children.Add(CreateInfoRow("إجمالي الإيرادات (جميع المواقع):", $"{report.TotalRevenueAllSites:F2} USD"));
            overallPanel.Children.Add(CreateInfoRow("الإيرادات المدفوعة (جميع المواقع):", $"{report.PaidRevenueAllSites:F2} USD"));
            overallPanel.Children.Add(CreateInfoRow("الإيرادات المعلقة (جميع المواقع):", $"{report.PendingRevenueAllSites:F2} USD"));
            overallPanel.Children.Add(CreateInfoRow("نسبة الدفع الإجمالية:", $"{report.OverallPaymentPercentage:F1}%"));
            overallPanel.Children.Add(CreateInfoRow("إجمالي الفواتير (جميع المواقع):", report.TotalInvoicesAllSites.ToString()));
            overallPanel.Children.Add(CreateInfoRow("الفواتير المدفوعة (جميع المواقع):", report.PaidInvoicesAllSites.ToString()));
            overallPanel.Children.Add(CreateInfoRow("نسبة إكمال الفواتير الإجمالية:", $"{report.OverallInvoiceCompletionPercentage:F1}%"));

            overallGroup.Content = overallPanel;
            pnlMultiSiteReport.Children.Add(overallGroup);

            // Site Comparison
            var siteComparisonGroup = CreateGroupBox("مقارنة المواقع");
            var siteComparisonPanel = new StackPanel();

            foreach (var siteReport in report.SiteReports)
            {
                var sitePanel = new Border
                {
                    BorderBrush = Brushes.LightGray,
                    BorderThickness = new Thickness(1),
                    Margin = new Thickness(0, 5, 0, 5),
                    Padding = new Thickness(10)
                };

                var siteContent = new StackPanel();
                siteContent.Children.Add(new TextBlock 
                { 
                    Text = siteReport.SiteDisplayName, 
                    FontWeight = FontWeights.Bold, 
                    FontSize = 14,
                    Margin = new Thickness(0, 0, 0, 5)
                });

                var siteStats = new StackPanel { Orientation = Orientation.Horizontal };
                siteStats.Children.Add(CreateStatBlock("الإيرادات", $"{siteReport.TotalRevenue:F2} USD"));
                siteStats.Children.Add(CreateStatBlock("المدفوع", $"{siteReport.PaidRevenue:F2} USD"));
                siteStats.Children.Add(CreateStatBlock("المعلق", $"{siteReport.PendingRevenue:F2} USD"));
                siteStats.Children.Add(CreateStatBlock("نسبة الدفع", $"{siteReport.PaymentPercentage:F1}%"));
                siteStats.Children.Add(CreateStatBlock("المشاريع", siteReport.TotalProjects.ToString()));
                siteStats.Children.Add(CreateStatBlock("الفواتير", siteReport.TotalInvoices.ToString()));

                siteContent.Children.Add(siteStats);
                sitePanel.Child = siteContent;
                siteComparisonPanel.Children.Add(sitePanel);
            }

            siteComparisonGroup.Content = siteComparisonPanel;
            pnlMultiSiteReport.Children.Add(siteComparisonGroup);
        }

        private Border CreateStatBlock(string label, string value)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(63, 81, 181)), // Material Design Primary Color
                CornerRadius = new CornerRadius(12),
                Margin = new Thickness(8),
                Padding = new Thickness(16),
                MinWidth = 120,
                MinHeight = 80,
                Effect = new DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 270,
                    ShadowDepth = 3,
                    Opacity = 0.4,
                    BlurRadius = 10
                }
            };

            var panel = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };
            panel.Children.Add(new TextBlock
            {
                Text = label,
                FontSize = 12,
                FontWeight = FontWeights.Medium,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 8)
            });
            panel.Children.Add(new TextBlock
            {
                Text = value,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center
            });

            border.Child = panel;
            return border;
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
