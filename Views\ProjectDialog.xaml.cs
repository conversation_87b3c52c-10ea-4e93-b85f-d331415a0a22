using System;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;

namespace FinancialTracker
{
    public partial class ProjectDialog : Window
    {
        private Project? _project;
        private bool _isEdit;

        public ProjectDialog(Project? project = null)
        {
            InitializeComponent();
            _project = project;
            _isEdit = project != null;

            if (_isEdit && _project != null)
            {
                Title = "Edit Project";
                NameTextBox.Text = _project.Name;
                DescriptionTextBox.Text = _project.Description;

                // Set status
                foreach (ComboBoxItem item in StatusComboBox.Items)
                {
                    if (item.Content?.ToString() == _project.Status)
                    {
                        StatusComboBox.SelectedItem = item;
                        break;
                    }
                }
            }
            else
            {
                Title = "Add New Project";
                StatusComboBox.SelectedIndex = 0; // Default to Active
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(NameTextBox.Text))
                {
                    MessageBox.Show("Project name is required.", "Validation Error", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (_isEdit && _project != null)
                {
                    _project.Name = NameTextBox.Text.Trim();
                    _project.Description = DescriptionTextBox.Text.Trim();
                    _project.Status = StatusComboBox.SelectedItem is ComboBoxItem selectedItem
                        ? selectedItem.Content?.ToString() ?? "Active"
                        : "Active";

                    await App.DataService.UpdateProjectAsync(_project);
                }
                else
                {
                    var newProject = new Project
                    {
                        Name = NameTextBox.Text.Trim(),
                        Description = DescriptionTextBox.Text.Trim(),
                        Status = StatusComboBox.SelectedItem is ComboBoxItem selectedItem
                            ? selectedItem.Content?.ToString() ?? "Active"
                            : "Active",
                        CreatedDate = DateTime.Now
                    };

                    await App.DataService.AddProjectAsync(newProject);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving project: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
