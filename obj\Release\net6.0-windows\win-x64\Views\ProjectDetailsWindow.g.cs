﻿#pragma checksum "..\..\..\..\..\Views\ProjectDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "064EFFBFEE8C4C92A0176D0A52A181A0AE0934CF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FinancialTracker;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FinancialTracker {
    
    
    /// <summary>
    /// ProjectDetailsWindow
    /// </summary>
    public partial class ProjectDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 34 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectNameText;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectNameDetail;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectStatusDetail;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectCreatedDetail;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectDescriptionDetail;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CommitmentsCountText;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CommitmentsTotalText;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoicesCountText;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoicesTotalText;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalInvoiceAmountText;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidAmountText;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnpaidAmountText;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CommitmentsBreakdownText;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearInvoiceFilterButton;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoicesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CommitmentSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearCommitmentFilterButton;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CommitmentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 379 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FinancialTracker;component/views/projectdetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProjectNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            
            #line 39 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditProjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 41 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ProjectNameDetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ProjectStatusDetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ProjectCreatedDetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ProjectDescriptionDetail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.CommitmentsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CommitmentsTotalText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.InvoicesCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.InvoicesTotalText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TotalInvoiceAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.PaidAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.UnpaidAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.CommitmentsBreakdownText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            
            #line 193 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.InvoiceSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 210 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            this.InvoiceSearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.InvoiceSearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.ClearInvoiceFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 220 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            this.ClearInvoiceFilterButton.Click += new System.Windows.RoutedEventHandler(this.ClearInvoiceFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.InvoicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 25:
            
            #line 299 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddCommitmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.CommitmentSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 316 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            this.CommitmentSearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CommitmentSearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 27:
            this.ClearCommitmentFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 326 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            this.ClearCommitmentFilterButton.Click += new System.Windows.RoutedEventHandler(this.ClearCommitmentFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.CommitmentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 34:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 20:
            
            #line 247 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 21:
            
            #line 249 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 22:
            
            #line 251 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenInvoiceLetterButton_Click);
            
            #line default
            #line hidden
            break;
            case 23:
            
            #line 254 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenInvoiceFileButton_Click);
            
            #line default
            #line hidden
            break;
            case 24:
            
            #line 257 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 29:
            
            #line 353 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditCommitmentButton_Click);
            
            #line default
            #line hidden
            break;
            case 30:
            
            #line 355 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyCommitmentButton_Click);
            
            #line default
            #line hidden
            break;
            case 31:
            
            #line 357 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewCommitmentInvoicesButton_Click);
            
            #line default
            #line hidden
            break;
            case 32:
            
            #line 359 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenCommitmentFileButton_Click);
            
            #line default
            #line hidden
            break;
            case 33:
            
            #line 362 "..\..\..\..\..\Views\ProjectDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteCommitmentButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

