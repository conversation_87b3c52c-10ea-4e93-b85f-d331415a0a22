using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FinancialTracker.Helpers;

namespace FinancialTracker.Models
{
    public class ProjectSite
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [Required]
        public int SiteId { get; set; }

        public DateTime AssignedDate { get; set; } = DateTime.Now;

        [MaxLength(50)]
        public string Status { get; set; } = "Active";

        [MaxLength(500)]
        public string? Notes { get; set; }

        // Budget and financial tracking per site
        [Column(TypeName = "decimal(18,2)")]
        public decimal? EstimatedBudget { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? ActualCost { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;

        [ForeignKey("SiteId")]
        public virtual Site Site { get; set; } = null!;

        // Computed properties
        [NotMapped]
        public decimal TotalInvoicesAmount => GetTotalInvoicesAmount();

        [NotMapped]
        public decimal PaidInvoicesAmount => GetPaidInvoicesAmount();

        [NotMapped]
        public decimal RemainingInvoicesAmount => TotalInvoicesAmount - PaidInvoicesAmount;

        [NotMapped]
        public int InvoicesCount => GetInvoicesCount();

        [NotMapped]
        public int PaidInvoicesCount => GetPaidInvoicesCount();

        [NotMapped]
        public int PendingInvoicesCount => InvoicesCount - PaidInvoicesCount;

        private decimal GetTotalInvoicesAmount()
        {
            if (Project?.Invoices == null) return 0;
            
            decimal total = 0;
            foreach (var invoice in Project.Invoices)
            {
                if (invoice.SiteId == SiteId)
                    total += invoice.AmountUSD;
            }
            return MathHelper.RoundCustom(total);
        }

        private decimal GetPaidInvoicesAmount()
        {
            if (Project?.Invoices == null) return 0;
            
            decimal total = 0;
            foreach (var invoice in Project.Invoices)
            {
                if (invoice.SiteId == SiteId)
                    total += invoice.PaidAmount;
            }
            return MathHelper.RoundCustom(total);
        }

        private int GetInvoicesCount()
        {
            if (Project?.Invoices == null) return 0;
            
            int count = 0;
            foreach (var invoice in Project.Invoices)
            {
                if (invoice.SiteId == SiteId)
                    count++;
            }
            return count;
        }

        private int GetPaidInvoicesCount()
        {
            if (Project?.Invoices == null) return 0;
            
            int count = 0;
            foreach (var invoice in Project.Invoices)
            {
                if (invoice.SiteId == SiteId && invoice.IsFullyPaid)
                    count++;
            }
            return count;
        }
    }
}
