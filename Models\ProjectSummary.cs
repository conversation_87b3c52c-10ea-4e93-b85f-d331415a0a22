using System;

using FinancialTracker.Helpers;

namespace FinancialTracker.Models
{
    public class ProjectSummary
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }

        // Financial summary properties
        private decimal _totalCost;
        private decimal _totalPaid;

        public decimal TotalCost
        {
            get => MathHelper.RoundCustom(_totalCost);
            set => _totalCost = value;
        }

        public decimal TotalPaid
        {
            get => MathHelper.RoundCustom(_totalPaid);
            set => _totalPaid = value;
        }

        public decimal Remaining => MathHelper.RoundCustom(TotalCost - TotalPaid);
        public double PaymentPercentage => TotalCost > 0 ? MathHelper.RoundCustom((double)(TotalPaid / TotalCost) * 100) : 0;

        // Additional info
        public int TotalInvoices { get; set; }
        public int TotalCommitments { get; set; }
        public int PaidInvoices { get; set; }
        public int UnpaidInvoices { get; set; }

        // إحصائيات مفصلة للارتباطات
        public int TaskCommitments { get; set; }
        public int ServiceCommitments { get; set; }
        public int OtherCommitments { get; set; }
        private decimal _taskCommitmentsAmount;
        private decimal _serviceCommitmentsAmount;
        private decimal _otherCommitmentsAmount;

        public decimal TaskCommitmentsAmount
        {
            get => MathHelper.RoundCustom(_taskCommitmentsAmount);
            set => _taskCommitmentsAmount = value;
        }

        public decimal ServiceCommitmentsAmount
        {
            get => MathHelper.RoundCustom(_serviceCommitmentsAmount);
            set => _serviceCommitmentsAmount = value;
        }

        public decimal OtherCommitmentsAmount
        {
            get => MathHelper.RoundCustom(_otherCommitmentsAmount);
            set => _otherCommitmentsAmount = value;
        }



        // نص مفصل للارتباطات (للعرض في تفاصيل المشروع)
        public string CommitmentsBreakdown => $"مهمات: {TaskCommitments} | خدمات: {ServiceCommitments}" +
                                            (OtherCommitments > 0 ? $" | أخرى: {OtherCommitments}" : "");

        // معلومات الملفات
        public int FilesCount { get; set; }
        public bool HasFiles => FilesCount > 0;
        public decimal TotalFileSize { get; set; } // بالميجابايت
        public string FileSizeFormatted => TotalFileSize > 0 ? $"{TotalFileSize:F1} MB" : "0 MB";
    }
}
