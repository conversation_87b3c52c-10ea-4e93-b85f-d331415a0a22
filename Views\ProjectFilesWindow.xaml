<Window x:Class="FinancialTracker.Views.ProjectFilesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Project Files Management"
        Height="700"
        Width="1100"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FolderOpen" Width="24" Height="24" Margin="0,0,8,0" Foreground="White"/>
                    <TextBlock x:Name="ProjectNameText" Text="Project Files" FontSize="18" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                            ToolTip="Refresh Files" Margin="4,0" Click="RefreshButton_Click">
                        <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20" Foreground="White"/>
                    </Button>
                    <Button x:Name="AddLetterButton" Content="Add Letter" Style="{StaticResource MaterialDesignFlatButton}"
                            Foreground="White" Margin="8,0" Click="AddLetterButton_Click"/>
                    <Button x:Name="AddFileButton" Content="Add File" Style="{StaticResource MaterialDesignFlatButton}"
                            Foreground="White" Margin="8,0" Click="AddFileButton_Click"/>
                    <Button Content="Close" Style="{StaticResource MaterialDesignFlatButton}"
                            Foreground="White" Margin="8,0" Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="16">
            <StackPanel>
                <!-- Summary Cards -->
                <Grid Margin="0,0,0,16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- First Row - File Counts -->
                    <Grid Grid.Row="0" Margin="0,0,0,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="12" MinHeight="60" Background="#E3F2FD">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="FileMultiple" Width="20" Height="20" Foreground="#1976D2" Margin="0,0,0,4"/>
                                <TextBlock Text="Total Files" Opacity="0.8" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="TotalFilesText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#1976D2" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="1" Margin="4" Padding="12" Background="#FFF3E0" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20" Foreground="#F57C00" Margin="0,0,0,4"/>
                                <TextBlock Text="Commitment Files" Opacity="0.8" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="CommitmentFilesText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#F57C00" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="2" Margin="4" Padding="12" Background="#E8F5E8" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="Receipt" Width="20" Height="20" Foreground="#388E3C" Margin="0,0,0,4"/>
                                <TextBlock Text="Invoice Files" Opacity="0.8" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="InvoiceFilesText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#388E3C" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <materialDesign:Card Grid.Column="3" Margin="4" Padding="12" Background="#F3E5F5" MinHeight="60">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="Email" Width="20" Height="20" Foreground="#7B1FA2" Margin="0,0,0,4"/>
                                <TextBlock Text="Letter Files" Opacity="0.8" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="LetterFilesText" Text="0" FontSize="18" FontWeight="Bold" Foreground="#7B1FA2" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>

                    <!-- Second Row - Size Info -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:Card Grid.Column="0" Margin="4" Padding="12" MinHeight="50" Background="#FFF8E1">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="HardDisk" Width="18" Height="18" Foreground="#FF8F00" Margin="0,0,0,4"/>
                                <TextBlock Text="Total Files Size" Opacity="0.8" FontSize="11" HorizontalAlignment="Center" TextAlignment="Center"/>
                                <TextBlock x:Name="TotalSizeText" Text="0 MB" FontSize="16" FontWeight="Bold" Foreground="#FF8F00" HorizontalAlignment="Center" TextAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </Grid>

                <!-- Files List -->
                <materialDesign:Card Padding="16">
                    <StackPanel>
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Files List" FontSize="16" FontWeight="Medium" VerticalAlignment="Center"/>
                            <ComboBox x:Name="FileTypeFilterComboBox" Grid.Column="1" Width="150"
                                      materialDesign:HintAssist.Hint="Filter by Type" SelectionChanged="FileTypeFilterComboBox_SelectionChanged">
                                <ComboBoxItem Content="All Files" IsSelected="True"/>
                                <ComboBoxItem Content="Invoice Files"/>
                                <ComboBoxItem Content="Commitment Files"/>
                                <ComboBoxItem Content="Letter Files"/>
                            </ComboBox>
                        </Grid>

                        <!-- Search Box -->
                        <TextBox x:Name="FileSearchTextBox" materialDesign:HintAssist.Hint="🔍 Search files..."
                                 Margin="0,0,0,16" TextChanged="FileSearchTextBox_TextChanged"/>

                        <DataGrid x:Name="FilesDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                  IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal"
                                  MaxHeight="350" HorizontalScrollBarVisibility="Auto">
                            <DataGrid.Columns>
                                <DataGridTemplateColumn Header="Type" Width="80">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="{Binding FileTypeIcon}" Width="20" Height="20"
                                                                       Foreground="{Binding FileTypeColor}" Margin="0,0,4,0"/>
                                                <TextBlock Text="{Binding FileType}" FontSize="10" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTextColumn Header="File Name" Binding="{Binding FileName}" Width="250"/>
                                <DataGridTextColumn Header="Source" Binding="{Binding Source}" Width="180"/>
                                <DataGridTextColumn Header="Size" Binding="{Binding FileSizeFormatted}" Width="80"/>
                                <DataGridTextColumn Header="Date Added" Binding="{Binding DateAdded, StringFormat='{}{0:yyyy-MM-dd}'}" Width="120"/>
                                <DataGridTemplateColumn Header="Actions" Width="280">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <Button Content="Open" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="2" Padding="8,4" FontSize="11" Click="OpenFileButton_Click"/>
                                                <Button Content="Copy Path" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="2" Padding="8,4" FontSize="11" Click="CopyPathButton_Click"/>
                                                <Button Content="Delete" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="2" Padding="8,4" FontSize="11" Click="DeleteFileButton_Click"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,8">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Information" Width="16" Height="16" Margin="0,0,8,0"/>
                    <TextBlock x:Name="StatusText" Text="Ready" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>
