<Window x:Class="FinancialTracker.ProjectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تفاصيل المشروع"
        Height="550"
        Width="500"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="معلومات المشروع" FontSize="20" FontWeight="Bold" Margin="0,0,0,16"/>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <TextBox x:Name="NameTextBox"
                         materialDesign:HintAssist.Hint="اسم المشروع"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="200"
                         Margin="0,0,0,16"/>

                <TextBox x:Name="DescriptionTextBox"
                         materialDesign:HintAssist.Hint="الوصف"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="1000"
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         Margin="0,0,0,16"/>

                <ComboBox x:Name="StatusComboBox"
                          materialDesign:HintAssist.Hint="حالة المشروع"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="0,0,0,16">
                    <ComboBoxItem Content="نشط"/>
                    <ComboBoxItem Content="معلق"/>
                    <ComboBoxItem Content="مكتمل"/>
                    <ComboBoxItem Content="ملغي"/>
                </ComboBox>

                <!-- PO Information Section -->
                <TextBlock Text="معلومات أمر الشراء (PO)" FontSize="16" FontWeight="Medium" Margin="0,8,0,12" Foreground="#1976D2"/>

                <DatePicker x:Name="PODatePicker"
                           materialDesign:HintAssist.Hint="تاريخ PO (تاريخ بداية المشروع)"
                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Margin="0,0,0,16"/>

                <TextBox x:Name="POAmountTextBox"
                         materialDesign:HintAssist.Hint="قيمة PO الكاملة (بالدولار)"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,0,16"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button Content="Cancel" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,8,0"
                    Padding="16,8"
                    Click="CancelButton_Click"/>
            <Button Content="Save" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Padding="16,8"
                    Click="SaveButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
