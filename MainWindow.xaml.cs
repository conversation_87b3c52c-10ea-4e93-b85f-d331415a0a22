#nullable enable
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using FinancialTracker.Models;
using FinancialTracker.Views;
using FinancialTracker.Services;

namespace FinancialTracker
{
    public partial class MainWindow : Window
    {
        private List<ProjectSummary> _allProjects = new List<ProjectSummary>();
        private List<ProjectSummary> _filteredProjects = new List<ProjectSummary>();

        public MainWindow()
        {
            InitializeComponent();
            LoadDashboardData();
        }

        private async void LoadDashboardData()
        {
            try
            {
                var dashboardData = await App.DataService.GetDashboardDataAsync();
                var projectsSummary = await App.DataService.GetProjectsSummaryAsync();

                // Check if data exists
                if (dashboardData == null)
                {
                    MessageBox.Show("Cannot load dashboard data", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Basic data - Check if elements exist
                if (TotalProjectsText != null) TotalProjectsText.Text = dashboardData.TotalProjects.ToString();
                if (TotalAmountText != null) TotalAmountText.Text = $"${dashboardData.TotalInvoiceAmount:N2}";
                if (TotalPaidText != null) TotalPaidText.Text = $"${dashboardData.TotalPaidAmount:N2}";
                if (TotalCommitmentsText != null) TotalCommitmentsText.Text = dashboardData.TotalCommitments.ToString();

                // Detailed commitment statistics
                if (TaskCommitmentsText != null) TaskCommitmentsText.Text = dashboardData.TaskCommitments.ToString();
                if (TaskCommitmentsAmountText != null) TaskCommitmentsAmountText.Text = $"${dashboardData.TaskCommitmentsAmount:N2}";

                if (ServiceCommitmentsText != null) ServiceCommitmentsText.Text = dashboardData.ServiceCommitments.ToString();
                if (ServiceCommitmentsAmountText != null) ServiceCommitmentsAmountText.Text = $"${dashboardData.ServiceCommitmentsAmount:N2}";

                if (OtherCommitmentsText != null) OtherCommitmentsText.Text = dashboardData.OtherCommitments.ToString();
                if (OtherCommitmentsAmountText != null) OtherCommitmentsAmountText.Text = $"${dashboardData.OtherCommitmentsAmount:N2}";

                // Save data for filtering
                _allProjects = projectsSummary?.ToList() ?? new List<ProjectSummary>();
                _filteredProjects = _allProjects.ToList();
                if (ProjectsSummaryDataGrid != null)
                    ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;


            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading dashboard data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // Initialize empty data to avoid errors
                _allProjects = new List<ProjectSummary>();
                _filteredProjects = new List<ProjectSummary>();
                if (ProjectsSummaryDataGrid != null)
                    ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
            }
        }

        // Navigation event handlers
        private void DashboardButton_Click(object sender, RoutedEventArgs e) => LoadDashboardData();



        // Project management event handlers
        private void AddProjectButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProjectDialog();
            if (dialog.ShowDialog() == true)
            {
                LoadDashboardData();
            }
        }

        private void ViewProjectDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            // Handle ProjectsSummaryDataGrid
            ProjectSummary? projectSummary = null;

            if (ProjectsSummaryDataGrid?.SelectedItem is ProjectSummary summary)
            {
                projectSummary = summary;
            }
            else
            {
                // Try to get from button's DataContext
                if (sender is Button button && button.DataContext is ProjectSummary summaryFromButton)
                {
                    projectSummary = summaryFromButton;
                }
            }

            int projectId = projectSummary?.Id ?? 0;

            if (projectId > 0)
            {
                var detailsWindow = new ProjectDetailsWindow(projectId);
                detailsWindow.Show();
            }
        }

        private async void EditProjectButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectSummary projectSummary)
            {
                try
                {
                    // Get the full project object
                    var project = await App.DataService.GetProjectByIdAsync(projectSummary.Id);
                    if (project != null)
                    {
                        var dialog = new ProjectDialog(project);
                        if (dialog.ShowDialog() == true)
                        {
                            LoadDashboardData();
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error loading project data: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void DeleteProjectButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectSummary projectSummary)
            {
                var result = MessageBox.Show($"Are you sure you want to delete project '{projectSummary.Name}'?\n\n⚠️ Warning: The following data will be permanently deleted:\n" +
                    $"• All invoices associated with the project across all sites\n" +
                    $"• All commitments associated with the project\n" +
                    $"• All replies and comments\n" +
                    $"• Project-site associations\n\n" +
                    $"This action cannot be undone!",
                    "Confirm Deletion", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var deleteResult = await App.DataService.DeleteProjectAsync(projectSummary.Id);
                        if (deleteResult)
                        {
                            LoadDashboardData();
                            MessageBox.Show($"Project '{projectSummary.Name}' and all associated data have been successfully deleted",
                                "Deleted", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("The project to be deleted was not found", "Error",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"An error occurred while deleting the project:\n\n{ex.Message}\n\nPlease try again or contact technical support.",
                            "Deletion Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }



        // Search and filter functions
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Search error: {ex.Message}");
            }
        }



        private void ApplyFilters()
        {
            try
            {
                if (_allProjects == null || !_allProjects.Any())
                {
                    _filteredProjects = new List<ProjectSummary>();
                    if (ProjectsSummaryDataGrid != null)
                        ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
                    return;
                }

                var filtered = _allProjects.AsEnumerable();

                // Text search filtering
                if (SearchTextBox != null && !string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    var searchText = SearchTextBox.Text.ToLower();
                    filtered = filtered.Where(p =>
                        (p.Name?.ToLower().Contains(searchText) ?? false) ||
                        (p.Description?.ToLower().Contains(searchText) ?? false));
                }

                // Status filtering


                _filteredProjects = filtered.ToList();
                if (ProjectsSummaryDataGrid != null)
                    ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Filter error: {ex.Message}");
                // In case of error, show all projects
                _filteredProjects = _allProjects?.ToList() ?? new List<ProjectSummary>();
                if (ProjectsSummaryDataGrid != null)
                    ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
            }
        }

        private void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SearchTextBox != null) SearchTextBox.Text = "";
                ApplyFilters();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Clear filters error: {ex.Message}");
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadDashboardData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // File management functions
        private void ViewProjectFilesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.DataContext is ProjectSummary projectSummary)
                {
                    var filesWindow = new ProjectFilesWindow(projectSummary.Id);
                    filesWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening files window: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


    }

    // Helper converter for enabling buttons based on file existence
    public class StringToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            return !string.IsNullOrEmpty(value as string);
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
