﻿#pragma checksum "..\..\..\..\..\Views\SiteManagementWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "03D3B698BD1A29DECD252386C7F25FD6ADF5E40E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FinancialTracker.Views {
    
    
    /// <summary>
    /// SiteManagementWindow
    /// </summary>
    public partial class SiteManagementWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 82 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddSite;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnEditSite;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRefresh;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnGenerateReport;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl tabControl;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox lstSites;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel pnlSiteDetails;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbSiteFilter;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnGenerateSiteReport;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel pnlReportContent;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel pnlMultiSiteReport;
        
        #line default
        #line hidden
        
        
        #line 289 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnGenerateMultiSiteReport;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtStatus;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FinancialTracker;component/views/sitemanagementwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnAddSite = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
            this.btnAddSite.Click += new System.Windows.RoutedEventHandler(this.BtnAddSite_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.btnEditSite = ((System.Windows.Controls.Button)(target));
            
            #line 95 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
            this.btnEditSite.Click += new System.Windows.RoutedEventHandler(this.BtnEditSite_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
            this.btnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.btnGenerateReport = ((System.Windows.Controls.Button)(target));
            
            #line 119 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
            this.btnGenerateReport.Click += new System.Windows.RoutedEventHandler(this.BtnGenerateReport_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.tabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 6:
            this.lstSites = ((System.Windows.Controls.ListBox)(target));
            
            #line 163 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
            this.lstSites.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.LstSites_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.pnlSiteDetails = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.cmbSiteFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 237 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
            this.cmbSiteFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbSiteFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.btnGenerateSiteReport = ((System.Windows.Controls.Button)(target));
            
            #line 246 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
            this.btnGenerateSiteReport.Click += new System.Windows.RoutedEventHandler(this.BtnGenerateSiteReport_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.pnlReportContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.pnlMultiSiteReport = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 12:
            this.btnGenerateMultiSiteReport = ((System.Windows.Controls.Button)(target));
            
            #line 292 "..\..\..\..\..\Views\SiteManagementWindow.xaml"
            this.btnGenerateMultiSiteReport.Click += new System.Windows.RoutedEventHandler(this.BtnGenerateMultiSiteReport_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.txtStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

